from src.utils.classes import Response
from src.modules.ratios_ef.models import RatioEF
from src.modules.ratios_ef.serializers import RatioEFSerializer, RatioEFSimpleSerializer
from django.core.exceptions import ObjectDoesNotExist
from src.modules.estado_financiero.models import EstadoFinanciero
from src.modules.ratios.models import Ratio
from datetime import datetime
from decimal import Decimal

class RatiosEFController:
    def __init__(self):
        pass

    def obtener_ratios_por_empresa_periodo(self, id_empresa, tipo_periodo, fecha_fin):
        """
        Obtiene todos los ratios de una empresa para un periodo específico.

        Args:
            id_empresa: ID de la empresa
            tipo_periodo: Tipo de periodo (1: anual, 2: trimestral, 3: mensual, etc.)
            fecha_fin: Fecha de fin del periodo (formato DD-MM-YYYY)

        Returns:
            Response: Objeto de respuesta con los ratios encontrados
        """
        try:
            # Convertir la fecha del formato DD-MM-YYYY al formato YYYY-MM-DD
            try:
                # Parsear la fecha en formato DD-MM-YYYY
                fecha_obj = datetime.strptime(fecha_fin, '%d-%m-%Y')
                # Convertir al formato YYYY-MM-DD para la consulta en la base de datos
                fecha_fin_formateada = fecha_obj.strftime('%Y-%m-%d')
            except ValueError:
                return Response(
                    message="Formato de fecha inválido. Debe ser DD-MM-YYYY",
                    state=False
                )

            # Buscar estados financieros que coincidan con los criterios
            estados_financieros = EstadoFinanciero.objects.filter(
                int_tipoRegistro=0,  # Solo periodos contables, no simulaciones
                int_idEmpresa_id=id_empresa,
                int_tipoPeriodo=tipo_periodo,
                dt_fechaFinPeriodo=fecha_fin_formateada
            )

            if not estados_financieros.exists():
                return Response(
                    message="No se encontraron estados financieros para los criterios especificados",
                    state=False
                )

            # Lista para almacenar los ratios
            ratios_list = []

            # Para cada estado financiero, obtener sus ratios
            for ef in estados_financieros:
                ratios_ef = RatioEF.objects.filter(int_idEstadoFinanciero=ef)

                for ratio_ef in ratios_ef:
                    # Obtener el nombre y la fórmula del ratio
                    nombre_ratio = ratio_ef.int_idRatios.str_descripcion
                    formula_ratio = ratio_ef.int_idRatios.str_formula
                    valor_ratio = ratio_ef.db_valorRatio

                    # Verificar si el ratio ya está en la lista (para evitar duplicados)
                    ratio_existente = next(
                        (r for r in ratios_list if r['nombre_ratio'] == nombre_ratio),
                        None
                    )

                    if not ratio_existente:
                        ratios_list.append({
                            'nombre_ratio': nombre_ratio,
                            'valor_ratio': float(valor_ratio),
                            'formula_ratio': formula_ratio
                        })

            if not ratios_list:
                return Response(
                    message="No se encontraron ratios para los estados financieros",
                    state=False
                )

            # Serializar los datos
            serializer = RatioEFSimpleSerializer(ratios_list, many=True)

            return Response(
                message="Ratios obtenidos exitosamente",
                data=serializer.data,
                state=True
            )

        except Exception as e:
            return Response(
                message=f"Error al obtener ratios: {str(e)}",
                state=False
            )

    def obtener_ratios_simulacion(self, id_estado_financiero):
        """
        Obtiene los ratios financieros para un estado financiero de simulación específico.

        Args:
            id_estado_financiero (int): ID del estado financiero de simulación

        Returns:
            Response: Objeto de respuesta con los ratios encontrados para la simulación
        """
        try:
            # Verificar que el estado financiero existe
            try:
                estado_financiero = EstadoFinanciero.objects.get(int_idEstadoFinanciero=id_estado_financiero)
            except ObjectDoesNotExist:
                return Response(
                    message=f"No se encontró el estado financiero con ID {id_estado_financiero}",
                    state=False
                )

            # Verificar que sea una simulación
            if estado_financiero.int_tipoRegistro != 1:
                return Response(
                    message=f"El estado financiero con ID {id_estado_financiero} no es una simulación",
                    state=False
                )

            # Obtener los ratios para este estado financiero
            ratios_ef = RatioEF.objects.filter(int_idEstadoFinanciero=estado_financiero)

            if not ratios_ef.exists():
                return Response(
                    message=f"No se encontraron ratios para el estado financiero con ID {id_estado_financiero}",
                    state=False
                )

            # Lista para almacenar los ratios
            ratios_list = []

            # Procesar cada ratio
            for ratio_ef in ratios_ef:
                # Obtener el nombre y la fórmula del ratio
                nombre_ratio = ratio_ef.int_idRatios.str_descripcion
                formula_ratio = ratio_ef.int_idRatios.str_formula
                valor_ratio = ratio_ef.db_valorRatio

                # Agregar el ratio a la lista
                ratios_list.append({
                    'nombre_ratio': nombre_ratio,
                    'valor_ratio': float(valor_ratio),
                    'formula_ratio': formula_ratio
                })

            # Serializar los datos
            serializer = RatioEFSimpleSerializer(ratios_list, many=True)

            return Response(
                message="Ratios de la simulación obtenidos exitosamente",
                data=serializer.data,
                state=True
            )

        except Exception as e:
            return Response(
                message=f"Error al obtener ratios de la simulación: {str(e)}",
                state=False
            )

    def obtener_ratios_ultimos_periodos(self, id_empresa, tipo_periodo, fecha_fin):
        """
        Obtiene los ratios financieros de los últimos 12 periodos para una empresa específica.

        Args:
            id_empresa (int): ID de la empresa
            tipo_periodo (int): ID del Tipo de periodo (1: anual, 2: trimestral, 3: mensual, 4: semanal, 5: diario)
            fecha_fin (str): Fecha de fin del periodo en formato DD-MM-YYYY

        Returns:
            Response: Objeto de respuesta con los ratios encontrados para los últimos periodos
        """
        try:
            # Convertir la fecha del formato DD-MM-YYYY al formato YYYY-MM-DD
            try:
                # Parsear la fecha en formato DD-MM-YYYY
                fecha_obj = datetime.strptime(fecha_fin, '%d-%m-%Y')
                # Convertir al formato YYYY-MM-DD para la consulta en la base de datos
                fecha_fin_formateada = fecha_obj.strftime('%Y-%m-%d')
            except ValueError:
                return Response(
                    message="Formato de fecha inválido. Debe ser DD-MM-YYYY",
                    state=False
                )

            # Obtener las fechas únicas de los últimos 12 periodos (incluyendo el actual)
            fechas_periodos = EstadoFinanciero.objects.filter(
                int_idEmpresa_id=id_empresa,
                int_tipoPeriodo=tipo_periodo,
                int_tipoRegistro=0,  # Solo periodos contables, no simulaciones
                dt_fechaFinPeriodo__lte=fecha_fin_formateada  # Fecha igual o anterior a la actual
            ).values_list('dt_fechaFinPeriodo', flat=True).distinct().order_by('-dt_fechaFinPeriodo')[:12]  # Limitar a los últimos 12 periodos

            if not fechas_periodos:
                return Response(
                    message=f"No se encontraron estados financieros para la empresa con ID {id_empresa} y tipo de periodo {tipo_periodo}",
                    state=False
                )

            resultado = []

            # Procesar cada periodo (fecha única)
            for fecha_periodo in fechas_periodos:
                # Formatear la fecha de fin para mostrar en el resultado
                fecha_fin_formateada_str = fecha_periodo.strftime('%d/%m/%Y')

                # Buscar todos los estados financieros para este periodo
                estados_financieros_periodo = EstadoFinanciero.objects.filter(
                    int_idEmpresa_id=id_empresa,
                    int_tipoPeriodo=tipo_periodo,
                    int_tipoRegistro=0,  # Solo periodos contables, no simulaciones
                    dt_fechaFinPeriodo=fecha_periodo
                )

                # Lista para almacenar los ratios de este periodo
                ratios_periodo = []

                # Para cada estado financiero, obtener sus ratios
                for ef in estados_financieros_periodo:
                    ratios_ef = RatioEF.objects.filter(int_idEstadoFinanciero=ef)

                    for ratio_ef in ratios_ef:
                        if ratio_ef.int_idRatios.str_descripcion in ["ROE", "ROA", "Margen Neto", "Rotación", "Apalancamiento"]:
                            # Obtener el nombre y la fórmula del ratio
                            nombre_ratio = ratio_ef.int_idRatios.str_descripcion
                            formula_ratio = ratio_ef.int_idRatios.str_formula
                            valor_ratio = ratio_ef.db_valorRatio

                            # Verificar si el ratio ya está en la lista (para evitar duplicados)
                            ratio_existente = next(
                                (r for r in ratios_periodo if r['nombre_ratio'] == nombre_ratio),
                                None
                            )

                            if not ratio_existente:
                                # Agregar el ratio a la lista de este periodo
                                ratios_periodo.append({
                                    'nombre_ratio': nombre_ratio,
                                    'valor_ratio': float(valor_ratio),
                                    'formula_ratio': formula_ratio
                                })

                # Si se encontraron ratios para este periodo, agregarlo al resultado
                if ratios_periodo:
                    periodo_data = {
                        "fecha_fin": fecha_fin_formateada_str,
                        "ratios": ratios_periodo
                    }
                    resultado.append(periodo_data)

            if not resultado:
                return Response(
                    message="No se encontraron ratios para los periodos especificados",
                    state=False
                )

            return Response(
                message="Ratios de los últimos periodos obtenidos exitosamente",
                data=resultado,
                state=True
            )

        except Exception as e:
            return Response(
                message=f"Error al obtener ratios de los últimos periodos: {str(e)}",
                state=False
            )