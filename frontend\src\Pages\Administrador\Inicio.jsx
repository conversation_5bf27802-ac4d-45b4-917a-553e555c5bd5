import React, { useState, useEffect } from "react";
import IconoSimulaciones from "../../assets/SVG/IconoSimulaciones";
import IconoUploadFile from "../../assets/SVG/IconoUploadFile";
import FileUploadModal from "../../Components/FileUploadModal/FileUploadModal";
import IconoFlechaSimular from "../../assets/SVG/IconoFlechaSimular";
import { RoutesPrivate } from "../../Routes/ProtectedRoute";
import { useNavigate } from "react-router-dom";
import IconoComparativa from "../../assets/SVG/IconoComparativa";
import FinancieraService from "../../Services/FinancieraService";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "../../Services/TokenService";
import { formatearFechaDDMMYYYY, obtenerNombreMesDesdeFecha, obtenerAñoDesdeFecha } from "../../utils/dateUtils";
import Swal from 'sweetalert2';
// import CustomPaginator from "../../Components/Paginator/Paginator";

const Inicio = () => {
  const navigate = useNavigate();
  // Primero, declara todos los estados básicos
  const [activosTotales, setActivosTotales] = useState({
    dataActual: "0",
    periodosAnteriores: {},
    variacion: "0"
  });

  const [pasivosTotales, setPasivosTotales] = useState({
    dataActual: "0",
    periodosAnteriores: {},
    variacion: "0"
  });

  const [ventasTotales, setVentasTotales] = useState({
    dataActual: "0",
    periodosAnteriores: {},
    variacion: "0"
  });

  const [utilidadNeta, setUtilidadNeta] = useState({
    dataActual: "0",
    periodosAnteriores: {},
    variacion: "0"
  });

  // Estados para el selector de empresas
  const [empresas, setEmpresas] = useState([]);
  const [empresaSeleccionada, setEmpresaSeleccionada] = useState(null);
  const [cargandoEmpresas, setCargandoEmpresas] = useState(false);
  const [errorEmpresas, setErrorEmpresas] = useState(null);

  // Estado para cargar datos financieros
  const [cargandoDatosFinancieros, setCargandoDatosFinancieros] = useState(false);
  const [errorDatosFinancieros, setErrorDatosFinancieros] = useState(null);

  // Estado para almacenar los datos originales del backend (con IDs de estados financieros)
  const [datosOriginalesBackend, setDatosOriginalesBackend] = useState([]);

  const [modalAbierto, setModalAbierto] = useState(false);
  const [mesSeleccionado, setMesSeleccionado] = useState(null);

  // Añadir nuevos estados para los filtros
  const [vistaSeleccionada, setVistaSeleccionada] = useState("Mensual");
  const [mesSeleccionadoFiltro, setMesSeleccionadoFiltro] = useState("Todos");

  // Estados para la moneda
  const [moneda, setMoneda] = useState("USD");
  const [simboloMoneda, setSimboloMoneda] = useState("$");

  // Función para cargar los datos financieros
  const cargarDatosFinancieros = async (tipoPeriodo, mesEspecifico = null) => {
    try {
      setCargandoDatosFinancieros(true);
      setErrorDatosFinancieros(null);

      let data;

      // Si estamos en vista anual y se seleccionó un mes específico
      if (vistaSeleccionada === "Anual" && mesEspecifico && mesEspecifico !== "Todos") {
        // Convertir nombre del mes a número (1-12)
        const mesesMap = {
          "Enero": 1, "Febrero": 2, "Marzo": 3, "Abril": 4, "Mayo": 5, "Junio": 6,
          "Julio": 7, "Agosto": 8, "Septiembre": 9, "Octubre": 10, "Noviembre": 11, "Diciembre": 12
        };
        const mesId = mesesMap[mesEspecifico];

        if (mesId) {
          // Llamar al endpoint específico para mes particular
          // console.log(`Llamando endpoint específico para mes ${mesEspecifico} (ID: ${mesId})`);
          data = await FinancieraService.getTotalAgrupadorPorMesAnual(mesId);
        } else {
          console.warn(`Mes no reconocido: ${mesEspecifico}. Usando función por defecto.`);
          data = await FinancieraService.getTotalAgrupadorUltimosPeriodos(tipoPeriodo);
        }
      } else {
        // Comportamiento normal: llamar a la función existente
        data = await FinancieraService.getTotalAgrupadorUltimosPeriodos(tipoPeriodo);
      }
      // console.log("Datos financieros recibidos del backend:", data);
      if (data) {
        // console.log("Datos financieros recibidos del backend:", data);
        // console.log("Tipo de periodo solicitado:", tipoPeriodo);
        // console.log("Mes específico:", mesEspecifico);

        // Almacenar los datos originales del backend para poder acceder a los IDs de estados financieros
        setDatosOriginalesBackend(data);

        // Optimización: Agrupar los agrupadores por Periodo para facilitar la iteración
        // Esto mueve todos los agrupadores de los estados financieros al nivel del periodo
        // para evitar búsquedas anidadas y mejorar el rendimiento
        let dataModificada = [];
        for (let i = 0; i < data.length; i++) {
          let periodo = data[i];
          let agrupadores = [];
          periodo.estados_financieros.forEach(ef => {
            agrupadores = agrupadores.concat(ef.agrupadores);
            delete ef.agrupadores;
          });
          periodo.agrupadores = agrupadores;
          dataModificada.push(periodo);
        }
        console.log("Datos financieros modificados:", dataModificada);

        // Generar dinámicamente la lista de nombres de agrupadores únicos
        actualizarNombresIndicadores(dataModificada);

        // Procesar los datos para las tarjetas principales
        procesarDatosTarjetas(dataModificada);

        // Procesar los datos para la tabla de periodos contables
        // Se ejecuta después de actualizar los nombres para asegurar que estén disponibles
        setTimeout(() => {
          procesarDatosMensuales(dataModificada);
        }, 0);
      }
    } catch (error) {
      console.error("Error al cargar datos financieros:", error);
      setErrorDatosFinancieros(error.message || "Error al cargar los datos financieros");
    } finally {
      setCargandoDatosFinancieros(false);
    }
  };

  // Función para procesar los datos de las tarjetas principales
  const procesarDatosTarjetas = (data) => {
    // Verificar si hay datos
    if (!data || data.length === 0) {
      // Establecer valores por defecto si no hay datos
      setActivosTotales({
        dataActual: `${simboloMoneda}0`,
        periodosAnteriores: {},
        variacion: "0%"
      });

      setPasivosTotales({
        dataActual: `${simboloMoneda}0`,
        periodosAnteriores: {},
        variacion: "0%"
      });

      setVentasTotales({
        dataActual: `${simboloMoneda}0`,
        periodosAnteriores: {},
        variacion: "0%"
      });

      setUtilidadNeta({
        dataActual: `${simboloMoneda}0`,
        periodosAnteriores: {},
        variacion: "0%"
      });

      return;
    }

    try {
      // Obtener el periodo más reciente (ya tiene agrupadores en el nivel superior)
      const periodoReciente = data[0];

      if (!periodoReciente || !periodoReciente.agrupadores || periodoReciente.agrupadores.length === 0) {
        console.warn("Periodo reciente sin agrupadores", periodoReciente);
        return;
      }

      // Crear mapa de búsqueda rápida para mejor rendimiento
      const mapaAgrupadores = crearMapaAgrupadores(periodoReciente.agrupadores);

      // Buscar los agrupadores principales con múltiples variantes de nombres
      const activosData = buscarAgrupadorEnMapa(mapaAgrupadores, ["activos totales", "ACTIVO TOTAL", "activos"]);
      const pasivosData = buscarAgrupadorEnMapa(mapaAgrupadores, ["pasivos totales", "pasivos"]);
      const ventasData = buscarAgrupadorEnMapa(mapaAgrupadores, ["ventas netas"]);
      const utilidadData = buscarAgrupadorEnMapa(mapaAgrupadores, ["ER-UN", "utilidad neta"]);

      // Actualizar estados con los datos encontrados
      // Activos Totales
      if (activosData && activosData.db_resultadoAgrupador !== undefined) {
        setActivosTotales({
          dataActual: formatearNumero(activosData.db_resultadoAgrupador, true, true),
          periodosAnteriores: generarDatosPeriodosAnteriores(data, ["activos totales", "ACTIVO TOTAL", "activos"]),
          variacion: activosData.str_porcentaje_variacion
        });
      } else {
        setActivosTotales({
          dataActual: `${simboloMoneda}0`,
          periodosAnteriores: generarDatosPeriodosAnteriores(data, ["activos totales", "ACTIVO TOTAL", "activos"]),
          variacion: null
        });
      }

      // Pasivos Totales
      if (pasivosData && pasivosData.db_resultadoAgrupador !== undefined) {
        setPasivosTotales({
          dataActual: formatearNumero(pasivosData.db_resultadoAgrupador, true, true),
          periodosAnteriores: generarDatosPeriodosAnteriores(data, ["pasivos totales", "pasivos"]),
          variacion: pasivosData.str_porcentaje_variacion
        });
      } else {
        setPasivosTotales({
          dataActual: `${simboloMoneda}0`,
          periodosAnteriores: generarDatosPeriodosAnteriores(data, ["pasivos totales", "pasivos"]),
          variacion: null
        });
      }

      // Ventas Totales
      if (ventasData && ventasData.db_resultadoAgrupador !== undefined) {
        setVentasTotales({
          dataActual: formatearNumero(ventasData.db_resultadoAgrupador, true, true),
          periodosAnteriores: generarDatosPeriodosAnteriores(data, ["ventas netas"]),
          variacion: ventasData.str_porcentaje_variacion
        });
      } else {
        setVentasTotales({
          dataActual: `${simboloMoneda}0`,
          periodosAnteriores: generarDatosPeriodosAnteriores(data, ["ventas netas"]),
          variacion: null
        });
      }

      // Utilidad Neta
      if (utilidadData && utilidadData.db_resultadoAgrupador !== undefined) {
        setUtilidadNeta({
          dataActual: formatearNumero(utilidadData.db_resultadoAgrupador, true, true),
          periodosAnteriores: generarDatosPeriodosAnteriores(data, ["ER-UN", "utilidad neta"]),
          variacion: utilidadData.str_porcentaje_variacion
        });
      } else {
        setUtilidadNeta({
          dataActual: `${simboloMoneda}0`,
          periodosAnteriores: generarDatosPeriodosAnteriores(data, ["ER-UN", "utilidad neta"]),
          variacion: null
        });
      }
    } catch (error) {
      console.error("Error general procesando datos de tarjetas:", error);
    }
  };

  // Función para generar datos de periodos anteriores para gráficos
  const generarDatosPeriodosAnteriores = (data, nombresAgrupador) => {
    const periodosAnteriores = {};

    if (!data || !Array.isArray(data)) {
      return periodosAnteriores;
    }

    try {
      // Recorrer todos los periodos
      data.forEach(periodo => {
        try {
          if (!periodo || !periodo.agrupadores || !Array.isArray(periodo.agrupadores) || periodo.agrupadores.length === 0) return;

          // Crear mapa de búsqueda rápida para mejor rendimiento
          const mapaAgrupadores = crearMapaAgrupadores(periodo.agrupadores);

          // Buscar el agrupador según los nombres proporcionados
          const agrupador = buscarAgrupadorEnMapa(mapaAgrupadores, nombresAgrupador);

          if (agrupador && agrupador.db_resultadoAgrupador !== undefined) {
            try {
              // Obtener la fecha del primer estado financiero
              const primerEstadoFinanciero = periodo.estados_financieros && periodo.estados_financieros[0];
              if (!primerEstadoFinanciero || !primerEstadoFinanciero.dt_fechaFinPeriodo) return;

              const fecha = new Date(primerEstadoFinanciero.dt_fechaFinPeriodo);
              if (isNaN(fecha.getTime())) return; // Verificar si la fecha es válida

              const año = fecha.getFullYear();
              periodosAnteriores[año] = formatearNumero(agrupador.db_resultadoAgrupador, false, false);
            } catch (error) {
              console.error("Error procesando fecha en generarDatosPeriodosAnteriores:", error);
            }
          }
        } catch (error) {
          console.error("Error procesando periodo en generarDatosPeriodosAnteriores:", error);
        }
      });
    } catch (error) {
      console.error("Error general en generarDatosPeriodosAnteriores:", error);
    }

    return periodosAnteriores;
  };

  // Función para obtener la moneda desde las cookies
  const obtenerMonedaDesdeCookies = () => {
    try {
      const monedaCookie = Cookies.get("monedaFinanciera");
      const simboloCookie = Cookies.get("simbMonedaFinanciera");

      if (monedaCookie && simboloCookie) {
        const monedaDecrypted = decrypt(monedaCookie);
        let simboloDecrypted = decrypt(simboloCookie);

        if (monedaDecrypted && simboloDecrypted) {
          // Regla especial: si el símbolo es "S/." quitar el punto final
          if (simboloDecrypted === "S/.") {
            simboloDecrypted = "S/";
          }

          setMoneda(monedaDecrypted);
          setSimboloMoneda(simboloDecrypted);
          return { moneda: monedaDecrypted, simbolo: simboloDecrypted };
        }
      }
    } catch (error) {
      console.error("Error al obtener moneda desde cookies:", error);
    }

    // Valores por defecto si no se pueden obtener desde cookies
    return { moneda: "USD", simbolo: "$" };
  };

  // Función para formatear números con separadores de miles y símbolo de moneda
  const formatearNumero = (numero, conSeparador = true, conMoneda = false) => {
    if (numero === null || numero === undefined) return conMoneda ? `${simboloMoneda}0` : "0";

    // Redondear a 2 decimales
    const numeroRedondeado = Math.round(numero * 100) / 100;

    let numeroFormateado;
    if (conSeparador) {
      // Formatear con separadores de miles usando coma como separador de miles
      numeroFormateado = numeroRedondeado.toLocaleString('es-ES').replace(/\./g, ',');
    } else {
      // Sin separadores para los datos de gráficos
      numeroFormateado = numeroRedondeado.toString();
    }

    // Agregar símbolo de moneda si se solicita
    return conMoneda ? `${simboloMoneda}${numeroFormateado}` : numeroFormateado;
  };

  // Función helper para crear un mapa de búsqueda rápida de agrupadores
  const crearMapaAgrupadores = (agrupadores) => {
    const mapa = new Map();
    agrupadores.forEach(agrupador => {
      if (agrupador && agrupador.nombre) {
        const nombreLower = agrupador.nombre.toLowerCase();
        mapa.set(nombreLower, agrupador);
      }
    });
    return mapa;
  };

  // Función para buscar agrupadores con múltiples variantes de nombres
  const buscarAgrupadorEnMapa = (mapa, nombres) => {
    const nombresArray = Array.isArray(nombres) ? nombres : [nombres];
    for (const nombre of nombresArray) {
      const agrupador = mapa.get(nombre.toLowerCase());
      if (agrupador) {
        return agrupador;
      }
    }
    return null;
  };

  // Mapeo de nombres técnicos a nombres amigables para mostrar en la tabla
  const obtenerNombreAmigable = (nombreTecnico) => {
    // Mapeo base de nombres técnicos a nombres amigables
    const mapeoNombresBase = {
      // Activos
      'activos totales': 'Activos Totales',
      'activos': 'Activos',
      'activo corriente': 'Activo Corriente',
      'activo no corriente': 'Activo No Corriente',

      // Pasivos
      'pasivos totales': 'Pasivos Totales',
      'pasivos': 'Pasivos',
      'pasivo corriente': 'Pasivo Corriente',
      'pasivo no corriente': 'Pasivo No Corriente',

      // Patrimonio
      'patrimonio': 'Patrimonio',

      // Ingresos y Ventas
      'ventas netas': 'Ventas Netas',
      'ingresos': 'Ingresos',
      'costo de ventas': 'Costo de Ventas',

      // Estados de Resultados
      'ER-UB': 'Utilidad Bruta',
      'ER-UO': 'Utilidad Operacional',
      'ER-UAIR': 'Utilidad Antes de Impuestos',
      'ER-UN': 'Utilidad Neta',

      // Gastos
      'gastos operacionales': 'Gastos Operacionales',
      'gastos administrativos': 'Gastos Administrativos',
      'gastos de ventas': 'Gastos de Ventas',
      'ingresos y gastos financieros': 'Ingresos y Gastos Financieros',
      'impuestos': 'Impuestos',

      // Otros
      'efectivo y equivalentes': 'Efectivo y Equivalentes',
      'inventarios': 'Inventarios',
      'cuentas por cobrar': 'Cuentas por Cobrar',
      'cuentas por pagar': 'Cuentas por Pagar',
      'propiedad planta y equipo': 'Propiedad, Planta y Equipo'
    };

    const nombreBase = mapeoNombresBase[nombreTecnico];
    if (nombreBase) {
      return nombreBase;
    }

    // Si no hay mapeo, formatear el nombre técnico (capitalizar primera letra de cada palabra)
    return nombreTecnico
      .split(' ')
      .map(palabra => palabra.charAt(0).toUpperCase() + palabra.slice(1).toLowerCase())
      .join(' ');
  };

  // Función para procesar los datos mensuales para la tabla
  const procesarDatosMensuales = (data) => {
    // console.log("=== PROCESANDO DATOS MENSUALES ===");
    // console.log("Data recibida:", data);
    // console.log("Nombres de indicadores disponibles:", nombresIndicadores);

    if (!data || data.length === 0) {
      // console.log("No hay datos para procesar, estableciendo array vacío");
      setDatosMensuales([]);
      return;
    }

    try {
      const nuevosDatosMensuales = data.map(periodo => {
        if (!periodo || !periodo.agrupadores || periodo.agrupadores.length === 0) {
          return null;
        }

        try {
          // Obtener el primer estado financiero para obtener la fecha
          const primerEstadoFinanciero = periodo.estados_financieros && periodo.estados_financieros[0];

          if (!primerEstadoFinanciero || !primerEstadoFinanciero.dt_fechaFinPeriodo) {
            console.warn("Estado financiero sin fecha de fin de periodo", primerEstadoFinanciero);
            return null;
          }

          // Obtener fecha y nombre del mes sin usar clase Date para evitar problemas de zona horaria
          const fechaString = primerEstadoFinanciero.dt_fechaFinPeriodo;
          const nombreMes = obtenerNombreMesDesdeFecha(fechaString);

          // Crear objeto de indicadores
          const indicadores = {};

          // Crear mapa de búsqueda rápida para mejor rendimiento
          const mapaAgrupadores = crearMapaAgrupadores(periodo.agrupadores);

          // Procesar cada indicador según la tabla de la imagen
          const procesarIndicador = (nombre) => {
            try {
              // Buscar el agrupador específico con múltiples variantes de nombres
              let agrupador;

              // Manejar casos especiales con múltiples variantes
              if (nombre.toLowerCase() === "activo total") {
                agrupador = buscarAgrupadorEnMapa(mapaAgrupadores, ["activos totales", "ACTIVO TOTAL", "activos"]);
              } else if (nombre.toLowerCase() === "activos totales") {
                agrupador = buscarAgrupadorEnMapa(mapaAgrupadores, ["activos totales", "ACTIVO TOTAL", "activos"]);
              } else if (nombre.toLowerCase() === "pasivos totales") {
                agrupador = buscarAgrupadorEnMapa(mapaAgrupadores, ["pasivos totales", "pasivos"]);
              } else if (nombre.toLowerCase() === "ventas totales") {
                agrupador = buscarAgrupadorEnMapa(mapaAgrupadores, ["ventas netas"]);
              } else if (nombre.toLowerCase() === "utilidad neta") {
                agrupador = buscarAgrupadorEnMapa(mapaAgrupadores, ["ER-UN", "utilidad neta"]);
              } else {
                // Buscar agrupador normal
                agrupador = buscarAgrupadorEnMapa(mapaAgrupadores, [nombre]);
              }

              if (agrupador && agrupador.db_resultadoAgrupador !== undefined) {
                const variacion = agrupador.str_porcentaje_variacion;

                // Si la variación es null, no mostrar porcentaje
                if (variacion === null || variacion === undefined) {
                  return {
                    valor: formatearNumero(agrupador.db_resultadoAgrupador, true, true),
                    porcentaje: null,
                    tendencia: "positiva",
                    colorGrafico: "#21DDB8"
                  };
                }

                // Limpiar la variación para poder convertirla a número
                const variacionLimpia = variacion.replace(/[+%]/g, '');
                const variacionNumero = parseFloat(variacionLimpia);

                return {
                  valor: formatearNumero(agrupador.db_resultadoAgrupador, true, true),
                  porcentaje: variacion.startsWith('+') ? variacion : (variacionNumero >= 0 ? `+${variacion}` : variacion),
                  tendencia: variacionNumero >= 0 ? "positiva" : "negativa",
                  colorGrafico: variacionNumero >= 0 ? "#21DDB8" : "#FF6B8A"
                };
              } else {
                // Valor por defecto si no se encuentra el agrupador
                return {
                  valor: `${simboloMoneda}0`,
                  porcentaje: "0%",
                  tendencia: "positiva",
                  colorGrafico: "#21DDB8"
                };
              }
            } catch (error) {
              console.error(`Error procesando indicador ${nombre}:`, error);
              // Valor por defecto en caso de error
              return {
                valor: `${simboloMoneda}0`,
                porcentaje: "0%",
                tendencia: "positiva",
                colorGrafico: "#21DDB8"
              };
            }
          };

          // Procesar cada indicador de la lista
          if (nombresIndicadores && nombresIndicadores.length > 0) {
            nombresIndicadores.forEach((nombre) => {
              indicadores[nombre] = procesarIndicador(nombre);
            });
          } else {
            // Si no hay nombres de indicadores, generar algunos básicos desde los agrupadores del periodo actual
            console.warn("No hay nombres de indicadores disponibles, generando básicos desde agrupadores del periodo");
            if (periodo.agrupadores && periodo.agrupadores.length > 0) {
              periodo.agrupadores.forEach((agrupador) => {
                if (agrupador && agrupador.nombre && agrupador.nombre.trim() !== '') {
                  const nombre = agrupador.nombre.trim();
                  indicadores[nombre] = procesarIndicador(nombre);
                }
              });
            }
          }

          return {
            nombre: nombreMes,
            fecha: formatearFechaDDMMYYYY(fechaString),
            indicadores
          };
        } catch (error) {
          console.error("Error procesando periodo:", error, periodo);
          return null;
        }
      }).filter(item => item !== null); // Filtrar elementos nulos

      // console.log("Datos mensuales procesados:", nuevosDatosMensuales);
      // console.log("Total de periodos procesados:", nuevosDatosMensuales.length);

      setDatosMensuales(nuevosDatosMensuales);

      // console.log("=== FIN PROCESAMIENTO DATOS MENSUALES ===");
    } catch (error) {
      console.error("Error general procesando datos mensuales:", error);
      setDatosMensuales([]);
    }
  };



  // Efecto para cargar las empresas al montar el componente
  useEffect(() => {
    const cargarEmpresas = async () => {
      try {
        setCargandoEmpresas(true);
        setErrorEmpresas(null);
        const data = await FinancieraService.getEmpresas();
        setEmpresas(data);
        // Si hay empresas, seleccionar la primera por defecto
        if (data && data.length > 0) {
          setEmpresaSeleccionada(data[0]);
        }
      } catch (error) {
        console.error("Error al cargar empresas:", error);
        setErrorEmpresas(error.message || "Error al cargar las empresas");
      } finally {
        setCargandoEmpresas(false);
      }
    };

    cargarEmpresas();
  }, []);

  // Efecto para cargar la información de moneda desde cookies
  useEffect(() => {
    obtenerMonedaDesdeCookies();
  }, [empresaSeleccionada]); // Se ejecuta cuando cambia la empresa seleccionada

  // Estado para la lista dinámica de nombres de agrupadores
  const [nombresIndicadores, setNombresIndicadores] = useState([]);

  // Función para generar dinámicamente la lista de nombres de agrupadores únicos
  const actualizarNombresIndicadores = (dataModificada) => {
    try {
      if (!dataModificada || dataModificada.length === 0) {
        setNombresIndicadores([]);
        return;
      }

      // Set para almacenar nombres únicos de agrupadores
      const nombresUnicos = new Set();

      // Recorrer todos los periodos para recopilar todos los nombres de agrupadores
      dataModificada.forEach(periodo => {
        if (periodo && periodo.agrupadores && Array.isArray(periodo.agrupadores)) {
          periodo.agrupadores.forEach(agrupador => {
            if (agrupador && agrupador.nombre && agrupador.nombre.trim() !== '') {
              nombresUnicos.add(agrupador.nombre.trim());
            }
          });
        }
      });

      // Convertir Set a Array y ordenar alfabéticamente
      const nombresOrdenados = Array.from(nombresUnicos).sort((a, b) => {
        // Ordenamiento personalizado: primero los que empiezan con mayúscula, luego alfabético
        const aUpper = a.toUpperCase();
        const bUpper = b.toUpperCase();

        // Priorizar ciertos agrupadores importantes al inicio
        const prioridades = [
          'activos totales',
          'activos',
          'activo corriente',
          'activo no corriente',
          'pasivos totales',
          'pasivos',
          'pasivo corriente',
          'pasivo no corriente',
          'patrimonio',
          'ventas netas',
          'costo de ventas',
          'ER-UN'
        ];

        const prioridadA = prioridades.findIndex(p => p.toLowerCase() === a.toLowerCase());
        const prioridadB = prioridades.findIndex(p => p.toLowerCase() === b.toLowerCase());

        // Si ambos tienen prioridad, ordenar por prioridad
        if (prioridadA !== -1 && prioridadB !== -1) {
          return prioridadA - prioridadB;
        }

        // Si solo uno tiene prioridad, ese va primero
        if (prioridadA !== -1) return -1;
        if (prioridadB !== -1) return 1;

        // Si ninguno tiene prioridad, ordenar alfabéticamente
        return aUpper.localeCompare(bUpper, 'es', { sensitivity: 'base' });
      });

      // console.log("Nombres de agrupadores generados dinámicamente:", nombresOrdenados);
      // console.log("Total de agrupadores únicos encontrados:", nombresOrdenados.length);
      setNombresIndicadores(nombresOrdenados);

    } catch (error) {
      console.error("Error al generar nombres de indicadores:", error);
      // En caso de error, usar una lista básica de respaldo
      setNombresIndicadores([
        'activos totales',
        'pasivos totales',
        'patrimonio',
        'ventas netas',
        'ER-UN'
      ]);
    }
  };



  // Declara datosMensuales antes de usarlo en cualquier otra función o variable
  const [datosMensuales, setDatosMensuales] = useState([
    // {
    //   nombre: "Abril",
    //   fecha: "31/04/2025",
    //   indicadores: {
    //     "Activos totales": {
    //       valor: "$1,245,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Pasivos totales": {
    //       valor: "$645,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     "Patrimonio": {
    //       valor: "$600,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Liquidez": {
    //       valor: "$1,245,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Endeudamiento": {
    //       valor: "$645,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     "Ratio 3": {
    //       valor: "$600,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //   },
    // },
    // {
    //   nombre: "Marzo",
    //   fecha: "31/03/2025",
    //   indicadores: {
    //     "Activos totales": {
    //       valor: "$1,245,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Pasivos totales": {
    //       valor: "$645,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     "Patrimonio": {
    //       valor: "$600,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Liquidez": {
    //       valor: "$1,245,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Endeudamiento": {
    //       valor: "$645,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     "Ratio 3": {
    //       valor: "$600,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //   },
    // },
    // {
    //   nombre: "Febrero",
    //   fecha: "31/02/2025",
    //   indicadores: {
    //     "Activos totales": {
    //       valor: "$1,245,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Pasivos totales": {
    //       valor: "$645,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     "Patrimonio": {
    //       valor: "$600,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Liquidez": {
    //       valor: "$1,245,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Endeudamiento": {
    //       valor: "$645,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     "Ratio 3": {
    //       valor: "$600,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //   },
    // },
    // {
    //   nombre: "Enero",
    //   fecha: "31/01/2025",
    //   indicadores: {
    //     "Activos totales": {
    //       valor: "$1,545,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Pasivos totales": {
    //       valor: "$685,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     "Patrimonio": {
    //       valor: "$680,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Liquidez": {
    //       valor: "$1,985,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Endeudamiento": {
    //       valor: "$685,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     "Ratio 3": {
    //       valor: "$680,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //   },
    // },
    // {
    //   nombre: "Diciembre",
    //   fecha: "31/12/2024",
    //   indicadores: {
    //     "Activos totales": {
    //       valor: "$1,545,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Pasivos totales": {
    //       valor: "$685,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     "Patrimonio": {
    //       valor: "$680,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Liquidez": {
    //       valor: "$1,985,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Endeudamiento": {
    //       valor: "$685,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     "Ratio 3": {
    //       valor: "$680,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //   },
    // },
    // {
    //   nombre: "Noviembre",
    //   fecha: "31/11/2024",
    //   indicadores: {
    //     "Activos totales": {
    //       valor: "$1,545,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Pasivos totales": {
    //       valor: "$685,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     "Patrimonio": {
    //       valor: "$680,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Liquidez": {
    //       valor: "$1,985,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Endeudamiento": {
    //       valor: "$685,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     "Ratio 3": {
    //       valor: "$680,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //   },
    // },
    // {
    //   nombre: "Octubre",
    //   fecha: "31/10/2024",
    //   indicadores: {
    //     "Activos totales": {
    //       valor: "$1,545,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Pasivos totales": {
    //       valor: "$685,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     "Patrimonio": {
    //       valor: "$680,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Liquidez": {
    //       valor: "$1,985,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Endeudamiento": {
    //       valor: "$685,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     "Ratio 3": {
    //       valor: "$680,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //   },
    // },
    // {
    //   nombre: "Septiembre",
    //   fecha: "31/09/2024",
    //   indicadores: {
    //     "Activos totales": {
    //       valor: "$1,545,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Pasivos totales": {
    //       valor: "$685,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     "Patrimonio": {
    //       valor: "$680,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Liquidez": {
    //       valor: "$1,985,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Endeudamiento": {
    //       valor: "$685,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     "Ratio 3": {
    //       valor: "$680,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //   },
    // },
  ]);

  // Ahora declara el startIndex y maxVisibleMonths
  const [startIndex, setStartIndex] = useState(0);
  const maxVisibleMonths = 5;

  // Obtener los datos según el filtro seleccionado
  const obtenerDatosFiltrados = () => {
    // En vista anual, los datos ya vienen filtrados del backend según la selección
    // En vista mensual, usar los datos mensuales normalmente
    return datosMensuales;
  };

  // Actualizar visibleMonths para usar los datos filtrados
  const datosFiltrados = obtenerDatosFiltrados();
  const visibleMonths = datosFiltrados.slice(
    startIndex,
    startIndex + maxVisibleMonths
  );

  // Funciones para abrir/cerrar el modal
  const abrirModal = (mes) => {
    setMesSeleccionado(mes);
    setModalAbierto(true);
  };

  const cerrarModal = () => {
    setModalAbierto(false);
    setMesSeleccionado(null);
  };

  // Función para mover hacia atrás (un mes a la vez)
  const handlePrevMonth = () => {
    if (startIndex > 0) {
      setStartIndex(startIndex - 1);
    }
  };

  // Función para mover hacia adelante (un mes a la vez)
  const handleNextMonth = () => {
    if (startIndex < datosFiltrados.length - maxVisibleMonths) {
      setStartIndex(startIndex + 1);
    }
  };

  // Función para ver detalles del periodo
  const verPeriodo = (fechaFin) => {
    console.log(`Ver detalles del periodo con fecha fin: ${fechaFin}`);

    // Buscar el periodo correspondiente en los datos originales del backend
    const periodoEncontrado = datosOriginalesBackend.find(periodo => {
      if (!periodo || !periodo.estados_financieros || periodo.estados_financieros.length === 0) {
        return false;
      }

      // Obtener el primer estado financiero para obtener la fecha
      const primerEstadoFinanciero = periodo.estados_financieros[0];
      if (!primerEstadoFinanciero || !primerEstadoFinanciero.dt_fechaFinPeriodo) {
        return false;
      }

      // Formatear la fecha del periodo para comparar con la fecha recibida
      const fechaFormateadaPeriodo = formatearFechaDDMMYYYY(primerEstadoFinanciero.dt_fechaFinPeriodo);

      return fechaFormateadaPeriodo === fechaFin;
    });

    if (periodoEncontrado) {
      // Obtener datos del primer estado financiero
      const primerEstadoFinanciero = periodoEncontrado.estados_financieros[0];
      const tipoPeriodo = primerEstadoFinanciero.int_tipoPeriodo; // 1: anual, 2: trimestral, 3: mensual, etc
      const fechaFinPeriodo = primerEstadoFinanciero.dt_fechaFinPeriodo;

      // Obtener el nombre del periodo para mostrar
      const nombreMes = obtenerNombreMesDesdeFecha(fechaFinPeriodo);

      console.log(`Navegando a DetalleEF con:`, {
        tipoPeriodo,
        fechaFin: fechaFin,
        nombrePeriodo: nombreMes
      });

      // Navegar pasando los parámetros como state
      navigate(RoutesPrivate.DETALLEEF, {
        state: {
          tipoPeriodo: tipoPeriodo,
          fechaFin: fechaFin,
          nombrePeriodo: nombreMes,
          periodoData: periodoEncontrado
        }
      });
    } else {
      console.warn(`No se encontró información del periodo con fecha fin: ${fechaFin}`);
      // Navegar sin parámetros si no se encuentra el periodo
      navigate(RoutesPrivate.DETALLEEF);
    }
  };

  // Función para eliminar periodo
  const eliminarPeriodo = async (fechaFin) => {
    try {
      // console.log(`Eliminar periodo con fecha: ${fechaFin}`);

      // Buscar el periodo correspondiente en los datos originales del backend
      const periodoEncontrado = datosOriginalesBackend.find(periodo => {
        if (!periodo || !periodo.estados_financieros || periodo.estados_financieros.length === 0) {
          return false;
        }

        // Obtener el primer estado financiero para obtener la fecha
        const primerEstadoFinanciero = periodo.estados_financieros[0];
        if (!primerEstadoFinanciero || !primerEstadoFinanciero.dt_fechaFinPeriodo) {
          return false;
        }

        // Formatear la fecha del periodo para comparar con la fecha recibida
        const fechaFormateadaPeriodo = formatearFechaDDMMYYYY(primerEstadoFinanciero.dt_fechaFinPeriodo);

        return fechaFormateadaPeriodo === fechaFin;
      });

      if (!periodoEncontrado) {
        console.error(`No se encontró el periodo con fecha ${fechaFin} en los datos originales`);
        Swal.fire({
          title: 'Error',
          text: `No se encontró el periodo con fecha ${fechaFin} en los datos disponibles.`,
          icon: 'error',
          confirmButtonText: 'Entendido',
          confirmButtonColor: '#1F263E'
        });
        return;
      }

      // Obtener todos los IDs de estados financieros de este periodo
      const idsEstadosFinancieros = periodoEncontrado.estados_financieros.map(ef => ef.int_idEstadoFinanciero);

      if (idsEstadosFinancieros.length === 0) {
        console.error(`No se encontraron estados financieros para el periodo con fecha ${fechaFin}`);
        Swal.fire({
          title: 'Sin datos',
          text: `No se encontraron estados financieros para el periodo con fecha ${fechaFin}.`,
          icon: 'info',
          confirmButtonText: 'Entendido',
          confirmButtonColor: '#1F263E'
        });
        return;
      }

      // console.log(`Estados financieros a eliminar para el periodo con fecha ${fechaFin}:`, idsEstadosFinancieros);

      // Determinar el tipo de periodo y formatear el nombre del periodo
      const primerEstadoFinanciero = periodoEncontrado.estados_financieros[0];
      const tipoPeriodo = primerEstadoFinanciero.int_tipoPeriodo; // 1: anual, 3: mensual
      const fechaString = primerEstadoFinanciero.dt_fechaFinPeriodo;
      const nombreMes = obtenerNombreMesDesdeFecha(fechaString);

      let nombrePeriodoCompleto;
      if (tipoPeriodo === 1) {
        // Periodo anual: mostrar solo el año
        const año = obtenerAñoDesdeFecha(fechaString);
        nombrePeriodoCompleto = año.toString();
      } else {
        // Periodo mensual: mostrar mes + año
        const año = obtenerAñoDesdeFecha(fechaString);
        nombrePeriodoCompleto = `${nombreMes} ${año}`;
      }

      // Confirmar eliminación con el usuario usando SweetAlert2
      const confirmacion = await Swal.fire({
        title: '⚠️ Confirmar eliminación',
        html: `
          <div style="text-align: left; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
            <p style="margin-bottom: 16px; color: #374151; font-size: 16px;">
              ¿Estás seguro de que deseas eliminar el periodo <strong>${nombrePeriodoCompleto}</strong>?
            </p>
            <div style="background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); padding: 16px; border-radius: 12px; border: 1px solid #f59e0b; margin-bottom: 16px;">
              <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <div style="width: 24px; height: 24px; background: #f59e0b; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                  <span style="color: white; font-weight: bold; font-size: 14px;">!</span>
                </div>
                <span style="font-weight: 600; color: #92400e;">Información importante</span>
              </div>
              <ul style="margin: 0; padding-left: 20px; color: #92400e;">
                <li>Se eliminarán <strong>${idsEstadosFinancieros.length} estado(s) financiero(s)</strong></li>
                <li>Esta acción <strong>no se puede deshacer</strong></li>
                <li>Los datos se perderán permanentemente</li>
              </ul>
            </div>
          </div>
        `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Sí, eliminar',
        cancelButtonText: 'Cancelar',
        confirmButtonColor: '#dc2626',
        cancelButtonColor: '#6b7280',
        reverseButtons: true,
        customClass: {
          popup: 'swal-delete-popup',
          confirmButton: 'swal-delete-confirm',
          cancelButton: 'swal-delete-cancel'
        },
        didOpen: () => {
          // Agregar estilos CSS personalizados
          const style = document.createElement('style');
          style.textContent = `
            .swal-delete-popup {
              border-radius: 16px !important;
              box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
            }
            .swal-delete-confirm, .swal-delete-cancel {
              border-radius: 8px !important;
              font-weight: 600 !important;
              padding: 12px 24px !important;
              transition: all 0.2s ease !important;
            }
            .swal-delete-confirm:hover {
              background-color: #b91c1c !important;
              transform: translateY(-1px) !important;
              box-shadow: 0 4px 12px rgba(220, 38, 38, 0.4) !important;
            }
            .swal-delete-cancel:hover {
              background-color: #4b5563 !important;
              transform: translateY(-1px) !important;
            }
          `;
          document.head.appendChild(style);
        }
      });

      if (!confirmacion.isConfirmed) {
        return;
      }

      // Mostrar indicador de carga
      setCargandoDatosFinancieros(true);

      // Eliminar cada estado financiero
      const resultadosEliminacion = [];
      let eliminacionesExitosas = 0;
      let eliminacionesFallidas = 0;

      for (const idEstadoFinanciero of idsEstadosFinancieros) {
        try {
          const resultado = await FinancieraService.deleteEstadoFinanciero(idEstadoFinanciero);
          resultadosEliminacion.push({
            id: idEstadoFinanciero,
            success: resultado.success,
            message: resultado.message
          });

          if (resultado.success) {
            eliminacionesExitosas++;
          } else {
            eliminacionesFallidas++;
          }
        } catch (error) {
          console.error(`Error eliminando estado financiero ${idEstadoFinanciero}:`, error);
          resultadosEliminacion.push({
            id: idEstadoFinanciero,
            success: false,
            message: error.message || 'Error desconocido'
          });
          eliminacionesFallidas++;
        }
      }

      // console.log('Resultados de eliminación:', resultadosEliminacion);

      // Mostrar resultado al usuario
      if (eliminacionesExitosas > 0 && eliminacionesFallidas === 0) {
        // Todas las eliminaciones fueron exitosas
        Swal.fire({
          title: '✅ ¡Eliminación exitosa!',
          html: `
            <div style="text-align: center; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
              <div style="background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%); padding: 20px; border-radius: 12px; margin-bottom: 16px; border: 1px solid #10b981;">
                <div style="font-size: 48px; margin-bottom: 12px;">🗑️</div>
                <p style="margin: 0; color: #065f46; font-size: 16px; font-weight: 600;">
                  Periodo <strong>${nombrePeriodoCompleto}</strong> eliminado exitosamente
                </p>
                <p style="margin: 8px 0 0 0; color: #047857; font-size: 14px;">
                  ${eliminacionesExitosas} estado(s) financiero(s) eliminado(s)
                </p>
              </div>
            </div>
          `,
          icon: 'success',
          confirmButtonText: 'Continuar',
          confirmButtonColor: '#10b981',
          customClass: {
            popup: 'swal-success-popup'
          },
          didOpen: () => {
            const style = document.createElement('style');
            style.textContent = `
              .swal-success-popup {
                border-radius: 16px !important;
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1) !important;
              }
            `;
            document.head.appendChild(style);
          }
        });

        // Refrescar los datos financieros
        if (empresaSeleccionada) {
          const tipoPeriodo = vistaSeleccionada === "Anual" ? 1 : 3;
          const mesParaCargar = vistaSeleccionada === "Anual" ? mesSeleccionadoFiltro : null;
          await cargarDatosFinancieros(tipoPeriodo, mesParaCargar);
        }
      } else if (eliminacionesExitosas > 0 && eliminacionesFallidas > 0) {
        // Algunas eliminaciones fueron exitosas, otras fallaron
        Swal.fire({
          title: '⚠️ Eliminación parcial',
          html: `
            <div style="text-align: left; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
              <div style="background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); padding: 16px; border-radius: 12px; margin-bottom: 16px; border: 1px solid #f59e0b;">
                <div style="display: flex; align-items: center; margin-bottom: 12px;">
                  <div style="width: 32px; height: 32px; background: #f59e0b; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                    <span style="color: white; font-weight: bold;">!</span>
                  </div>
                  <span style="font-weight: 600; color: #92400e; font-size: 16px;">Eliminación parcial del periodo ${nombrePeriodoCompleto}</span>
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 12px;">
                  <div style="background: #d1fae5; padding: 12px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 24px; font-weight: bold; color: #065f46;">${eliminacionesExitosas}</div>
                    <div style="font-size: 12px; color: #047857;">Exitosas</div>
                  </div>
                  <div style="background: #fee2e2; padding: 12px; border-radius: 8px; text-align: center;">
                    <div style="font-size: 24px; font-weight: bold; color: #991b1b;">${eliminacionesFallidas}</div>
                    <div style="font-size: 12px; color: #dc2626;">Fallidas</div>
                  </div>
                </div>
                <p style="margin: 0; color: #92400e; font-size: 14px; text-align: center;">
                  Revisa la consola del navegador para más detalles sobre los errores.
                </p>
              </div>
            </div>
          `,
          icon: 'warning',
          confirmButtonText: 'Entendido',
          confirmButtonColor: '#f59e0b'
        });

        // Refrescar los datos financieros para mostrar el estado actual
        if (empresaSeleccionada) {
          const tipoPeriodo = vistaSeleccionada === "Anual" ? 1 : 3;
          const mesParaCargar = vistaSeleccionada === "Anual" ? mesSeleccionadoFiltro : null;
          await cargarDatosFinancieros(tipoPeriodo, mesParaCargar);
        }
      } else {
        // Todas las eliminaciones fallaron
        Swal.fire({
          title: '❌ Error en la eliminación',
          html: `
            <div style="text-align: left; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
              <div style="background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%); padding: 16px; border-radius: 12px; margin-bottom: 16px; border: 1px solid #ef4444;">
                <div style="display: flex; align-items: center; margin-bottom: 12px;">
                  <div style="width: 32px; height: 32px; background: #ef4444; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                    <span style="color: white; font-weight: bold;">✕</span>
                  </div>
                  <span style="font-weight: 600; color: #991b1b; font-size: 16px;">No se pudo eliminar el periodo ${nombrePeriodoCompleto}</span>
                </div>
                <div style="background: #fef2f2; padding: 12px; border-radius: 8px; border-left: 4px solid #ef4444;">
                  <p style="margin: 0; color: #991b1b; font-size: 14px;">
                    <strong>Motivo:</strong> No se pudo eliminar ningún estado financiero del periodo.
                  </p>
                  <p style="margin: 8px 0 0 0; color: #7f1d1d; font-size: 12px;">
                    Revisa la consola del navegador para obtener información detallada sobre los errores.
                  </p>
                </div>
              </div>
            </div>
          `,
          icon: 'error',
          confirmButtonText: 'Entendido',
          confirmButtonColor: '#ef4444'
        });
      }

    } catch (error) {
      console.error('Error general en eliminación de periodo:', error);
      Swal.fire({
        title: '💥 Error inesperado',
        html: `
          <div style="text-align: left; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
            <div style="background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%); padding: 16px; border-radius: 12px; margin-bottom: 16px; border: 1px solid #ef4444;">
              <div style="display: flex; align-items: center; margin-bottom: 12px;">
                <div style="width: 32px; height: 32px; background: #ef4444; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                  <span style="color: white; font-weight: bold;">!</span>
                </div>
                <span style="font-weight: 600; color: #991b1b; font-size: 16px;">Error inesperado</span>
              </div>
              <p style="margin: 0 0 12px 0; color: #991b1b; font-size: 14px;">
                Ha ocurrido un error inesperado al intentar eliminar el periodo con fecha <strong>${fechaFin}</strong>.
              </p>
              <div style="background: #fef2f2; padding: 12px; border-radius: 8px; border-left: 4px solid #ef4444;">
                <p style="margin: 0; color: #7f1d1d; font-size: 12px; font-family: monospace; word-break: break-word;">
                  <strong>Detalles del error:</strong><br>
                  ${error.message || 'Error desconocido'}
                </p>
              </div>
            </div>
          </div>
        `,
        icon: 'error',
        confirmButtonText: 'Entendido',
        confirmButtonColor: '#ef4444',
        customClass: {
          popup: 'swal-error-popup'
        },
        didOpen: () => {
          const style = document.createElement('style');
          style.textContent = `
            .swal-error-popup {
              border-radius: 16px !important;
              box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1) !important;
            }
          `;
          document.head.appendChild(style);
        }
      });
    } finally {
      setCargandoDatosFinancieros(false);
    }
  };


  // Manejar cambio de vista
  const handleVistaChange = (vista) => {
    setVistaSeleccionada(vista);
    // setStartIndex(0); // Resetear a la primera página
  };

  // Manejar cambio de mes
  const handleMesChange = (mes) => {
    setMesSeleccionadoFiltro(mes);
    // setStartIndex(0); // Resetear a la primera página

    // Si estamos en vista anual, recargar datos según la selección
    if (vistaSeleccionada === "Anual" && empresaSeleccionada) {
      const tipoPeriodo = 1; // Anual
      cargarDatosFinancieros(tipoPeriodo, mes);
    }
  };

  // Manejar cambio de empresa
  const handleEmpresaChange = (empresa) => {
    setEmpresaSeleccionada(empresa);

    // Guardar el ID de la empresa en las cookies usando encrypt
    if (empresa && empresa.int_idEmpresa) {
      Cookies.set("busFinanciera", encrypt(empresa.int_idEmpresa.toString()));
      // Guardar en cookies moneda y simbolo
      Cookies.set("monedaFinanciera", encrypt(empresa.str_Moneda));
      Cookies.set("simbMonedaFinanciera", encrypt(empresa.str_SimboloMoneda));
      console.log("Empresa seleccionada guardado en cookies:", empresa);
    }
  };

  // Efecto para cargar datos financieros cuando cambia la empresa seleccionada
  useEffect(() => {
    if (empresaSeleccionada) {
      // Cargar datos para el tipo de periodo seleccionado (3 = mensual por defecto)
      const tipoPeriodo = vistaSeleccionada === "Anual" ? 1 : 3;
      const mesParaCargar = vistaSeleccionada === "Anual" ? mesSeleccionadoFiltro : null;
      cargarDatosFinancieros(tipoPeriodo, mesParaCargar);
    }
  }, [empresaSeleccionada, vistaSeleccionada]);

  // Efecto para reprocesar datos mensuales cuando cambien los nombres de indicadores
  useEffect(() => {
    if (nombresIndicadores.length > 0 && datosOriginalesBackend.length > 0) {
      // console.log("Reprocesando datos mensuales debido a cambio en nombres de indicadores");
      procesarDatosMensuales(datosOriginalesBackend);
    }
  }, [nombresIndicadores]);

  return (
    <div className="w-full border-box flex flex-col justify-start items-center gap-3">
      <div className="w-full flex flex-col justify-start items-start gap-4">
        <div className="flex   md:flex-row justify-between items-center md:items-center w-full gap-3 md:gap-0">
          <button
            className="group bg-[#FFFF] text-[#1F263E] p-3 rounded-lg border-[#E4E4E4] border-2 gap-2 flex items-center justify-center hover:bg-[#1F263E] hover:text-[#FFFF] cursor-pointer hover:border-[#1F263E]"
            onClick={() => navigate(RoutesPrivate.SIMULACIONES)}
          >
            <IconoSimulaciones
              size={"1.2rem"}
              className="text-[#1F263E] group-hover:text-[#FFFF]"
            />{" "}
            Simulaciones
            <IconoFlechaSimular
              size={"1.2rem"}
              className="text-[#1F263E] group-hover:text-[#FFFF]"
            />
          </button>
          <select
            name="empresa"
            id="empresa"
            className="border border-gray-400 rounded-md p-1 min-w-[13rem] outline-none"
            value={empresaSeleccionada?.int_idEmpresa || ""}
            onChange={(e) => {
              const empresaId = parseInt(e.target.value);
              const empresa = empresas.find(emp => emp.int_idEmpresa === empresaId);
              handleEmpresaChange(empresa);
            }}
          >
            {cargandoEmpresas ? (
              <option value="" disabled>Cargando empresas...</option>
            ) : errorEmpresas ? (
              <option value="" disabled>Error al cargar empresas</option>
            ) : empresas.length === 0 ? (
              <option value="" disabled>No hay empresas disponibles</option>
            ) : (
              empresas.map((empresa) => (
                <option key={empresa.int_idEmpresa} value={empresa.int_idEmpresa}>
                  {empresa.str_NombreEmpresa}
                </option>
              ))
            )}
          </select>
        </div>
        <div className="flex flex-wrap gap-4 justify-start items-start w-full border-box">
          {cargandoDatosFinancieros ? (
            <div className="w-full flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#21DDB8]"></div>
            </div>
          ) : errorDatosFinancieros ? (
            <div className="w-full flex justify-center items-center py-8 text-red-500">
              {errorDatosFinancieros}
            </div>
          ) : (
            <>
          <div className="w-[calc(50%-0.5rem)] md:w-[calc(25%-0.75rem)] border-2 border-[#EFEFEF] rounded-xl flex flex-col justify-start items-start gap-1 py-2">
            <span className="text-[#1F263E] text-lg font-semibold px-4">
              {" "}
              Activos Totales
            </span>
            <span className="text-[#1F263E] text-2xl md:text-3xl font-semibold px-4">
              {" "}
              {activosTotales.dataActual}
            </span>
            {activosTotales.variacion !== null && (
              <span className={`${parseFloat(activosTotales.variacion) >= 0 ? "text-[#31C969]" : "text-[#FF6B8A]"} text-sm md:text-md font-base px-4`}>
                {parseFloat(activosTotales.variacion) >= 0 ? "" : ""}{activosTotales.variacion} desde el último periodo{" "}
              </span>
            )}
            <div className="w-full h-17 mt-1">
              <svg
                viewBox="0 0 100 20"
                className="w-full h-full"
                preserveAspectRatio="none"
              >
                <defs>
                  <linearGradient
                    id="gradient"
                    x1="0%"
                    y1="0%"
                    x2="0%"
                    y2="100%"
                  >
                    <stop offset="0%" stopColor="#21DDB8" stopOpacity="0.6" />
                    <stop offset="100%" stopColor="#21DDB8" stopOpacity="0.1" />
                  </linearGradient>
                </defs>
                {(() => {
                  // Convertir los datos a números
                  const years = Object.keys(
                    activosTotales.periodosAnteriores
                  ).sort();
                  const values = years.map((year) => {
                    const value = activosTotales.periodosAnteriores[year];
                    return parseFloat(value);
                  });

                  // Verificar si hay valores
                  if (values.length === 0) {
                    return null;
                  }

                  // Encontrar el valor máximo para normalizar
                  const maxValue = Math.max(...values) || 1; // Usar 1 como valor por defecto si maxValue es 0

                  // Verificar si hay valores válidos
                  if (maxValue === 0) {
                    return null;
                  }

                  // Crear puntos para la curva
                  const points = values.map((value, index) => {
                    const x = values.length === 1 ? 50 : (index / (values.length - 1)) * 100;
                    const y = 20 - (value / maxValue) * 15; // Reducir altura máxima a 15
                    return { x, y };
                  });

                  // Verificar si hay suficientes puntos para crear una curva
                  if (points.length === 0) {
                    return null; // No hay datos para mostrar
                  }

                  // Crear la curva
                  let pathD = `M${points[0].x},${points[0].y}`;

                  // Si solo hay un punto, dibujamos una línea horizontal
                  if (points.length === 1) {
                    pathD = `M0,${points[0].y} L100,${points[0].y}`;
                  } else {
                    // Crear curva con múltiples puntos
                    for (let i = 0; i < points.length - 1; i++) {
                      const cp1x =
                        points[i].x + (points[i + 1].x - points[i].x) / 3;
                      const cp1y = points[i].y;
                      const cp2x =
                        points[i].x + (2 * (points[i + 1].x - points[i].x)) / 3;
                      const cp2y = points[i + 1].y;
                      pathD += ` C${cp1x},${cp1y} ${cp2x},${cp2y} ${
                        points[i + 1].x
                      },${points[i + 1].y}`;
                    }
                  }

                  // Crear el área
                  const areaD = points.length === 1
                    ? `M0,${points[0].y} L100,${points[0].y} V20 H0 Z`
                    : `${pathD} V20 H${points[0].x} Z`;

                  return (
                    <>
                      <path
                        d={pathD}
                        fill="none"
                        stroke="#21DDB8"
                        strokeWidth="0.5"
                      />
                      <path d={areaD} fill="url(#gradient)" />
                    </>
                  );
                })()}
              </svg>
            </div>
          </div>
          <div className="w-[calc(50%-0.5rem)] md:w-[calc(25%-0.75rem)] border-2 border-[#EFEFEF] rounded-xl flex flex-col justify-start items-start gap-1 py-2">
            <span className="text-[#1F263E] text-lg font-semibold px-4">
              {" "}
              Pasivos Totales
            </span>
            <span className="text-[#1F263E] text-2xl md:text-3xl font-semibold px-4">
              {" "}
              {pasivosTotales.dataActual}
            </span>
            {pasivosTotales.variacion !== null && (
              <span className={`${parseFloat(pasivosTotales.variacion) >= 0 ? "text-[#31C969]" : "text-[#FF6B8A]"} text-sm md:text-md font-base px-4`}>
                {parseFloat(pasivosTotales.variacion) >= 0 ? "" : ""}{pasivosTotales.variacion} desde el último periodo{" "}
              </span>
            )}
            <div className="w-full h-17 mt-1">
              <svg
                viewBox="0 0 100 20"
                className="w-full h-full"
                preserveAspectRatio="none"
              >
                <defs>
                  <linearGradient
                    id="gradient"
                    x1="0%"
                    y1="0%"
                    x2="0%"
                    y2="100%"
                  >
                    <stop offset="0%" stopColor="#21DDB8" stopOpacity="0.6" />
                    <stop offset="100%" stopColor="#21DDB8" stopOpacity="0.1" />
                  </linearGradient>
                </defs>
                {(() => {
                  // Convertir los datos a números
                  const years = Object.keys(
                    pasivosTotales.periodosAnteriores
                  ).sort();
                  const values = years.map((year) => {
                    const value = pasivosTotales.periodosAnteriores[year];
                    return parseFloat(value);
                  });

                  // Verificar si hay valores
                  if (values.length === 0) {
                    return null;
                  }

                  // Encontrar el valor máximo para normalizar
                  const maxValue = Math.max(...values) || 1; // Usar 1 como valor por defecto si maxValue es 0

                  // Verificar si hay valores válidos
                  if (maxValue === 0) {
                    return null;
                  }

                  // Crear puntos para la curva
                  const points = values.map((value, index) => {
                    const x = values.length === 1 ? 50 : (index / (values.length - 1)) * 100;
                    const y = 20 - (value / maxValue) * 15; // Reducir altura máxima a 15
                    return { x, y };
                  });

                  // Verificar si hay suficientes puntos para crear una curva
                  if (points.length === 0) {
                    return null; // No hay datos para mostrar
                  }

                  // Crear la curva
                  let pathD = `M${points[0].x},${points[0].y}`;

                  // Si solo hay un punto, dibujamos una línea horizontal
                  if (points.length === 1) {
                    pathD = `M0,${points[0].y} L100,${points[0].y}`;
                  } else {
                    // Crear curva con múltiples puntos
                    for (let i = 0; i < points.length - 1; i++) {
                      const cp1x =
                        points[i].x + (points[i + 1].x - points[i].x) / 3;
                      const cp1y = points[i].y;
                      const cp2x =
                        points[i].x + (2 * (points[i + 1].x - points[i].x)) / 3;
                      const cp2y = points[i + 1].y;
                      pathD += ` C${cp1x},${cp1y} ${cp2x},${cp2y} ${
                        points[i + 1].x
                      },${points[i + 1].y}`;
                    }
                  }

                  // Crear el área
                  const areaD = points.length === 1
                    ? `M0,${points[0].y} L100,${points[0].y} V20 H0 Z`
                    : `${pathD} V20 H${points[0].x} Z`;

                  return (
                    <>
                      <path
                        d={pathD}
                        fill="none"
                        stroke="#21DDB8"
                        strokeWidth="0.5"
                      />
                      <path d={areaD} fill="url(#gradient)" />
                    </>
                  );
                })()}
              </svg>
            </div>
          </div>
          <div className="w-[calc(50%-0.5rem)] md:w-[calc(25%-0.75rem)] border-2 border-[#EFEFEF] rounded-xl flex flex-col justify-start items-start gap-1 py-2">
            <span className="text-[#1F263E] text-lg font-semibold px-4">
              {" "}
              Ventas Totales
            </span>
            <span className="text-[#1F263E] text-2xl md:text-3xl font-semibold px-4">
              {" "}
              {ventasTotales.dataActual}
            </span>
            {ventasTotales.variacion !== null && (
              <span className={`${parseFloat(ventasTotales.variacion) >= 0 ? "text-[#31C969]" : "text-[#FF6B8A]"} text-sm md:text-md font-base px-4`}>
                {parseFloat(ventasTotales.variacion) >= 0 ? "" : ""}{ventasTotales.variacion} desde el último periodo{" "}
              </span>
            )}
            <div className="w-full h-17 mt-1">
              <svg
                viewBox="0 0 100 20"
                className="w-full h-full"
                preserveAspectRatio="none"
              >
                <defs>
                  <linearGradient
                    id="gradient"
                    x1="0%"
                    y1="0%"
                    x2="0%"
                    y2="100%"
                  >
                    <stop offset="0%" stopColor="#21DDB8" stopOpacity="0.6" />
                    <stop offset="100%" stopColor="#21DDB8" stopOpacity="0.1" />
                  </linearGradient>
                </defs>
                {(() => {
                  // Convertir los datos a números
                  const years = Object.keys(
                    ventasTotales.periodosAnteriores
                  ).sort();
                  const values = years.map((year) => {
                    const value = ventasTotales.periodosAnteriores[year];
                    return parseFloat(value);
                  });

                  // Verificar si hay valores
                  if (values.length === 0) {
                    return null;
                  }

                  // Encontrar el valor máximo para normalizar
                  const maxValue = Math.max(...values) || 1; // Usar 1 como valor por defecto si maxValue es 0

                  // Verificar si hay valores válidos
                  if (maxValue === 0) {
                    return null;
                  }

                  // Crear puntos para la curva
                  const points = values.map((value, index) => {
                    const x = values.length === 1 ? 50 : (index / (values.length - 1)) * 100;
                    const y = 20 - (value / maxValue) * 15; // Reducir altura máxima a 15
                    return { x, y };
                  });

                  // Verificar si hay suficientes puntos para crear una curva
                  if (points.length === 0) {
                    return null; // No hay datos para mostrar
                  }

                  // Crear la curva
                  let pathD = `M${points[0].x},${points[0].y}`;

                  // Si solo hay un punto, dibujamos una línea horizontal
                  if (points.length === 1) {
                    pathD = `M0,${points[0].y} L100,${points[0].y}`;
                  } else {
                    // Crear curva con múltiples puntos
                    for (let i = 0; i < points.length - 1; i++) {
                      const cp1x =
                        points[i].x + (points[i + 1].x - points[i].x) / 3;
                      const cp1y = points[i].y;
                      const cp2x =
                        points[i].x + (2 * (points[i + 1].x - points[i].x)) / 3;
                      const cp2y = points[i + 1].y;
                      pathD += ` C${cp1x},${cp1y} ${cp2x},${cp2y} ${
                        points[i + 1].x
                      },${points[i + 1].y}`;
                    }
                  }

                  // Crear el área
                  const areaD = points.length === 1
                    ? `M0,${points[0].y} L100,${points[0].y} V20 H0 Z`
                    : `${pathD} V20 H${points[0].x} Z`;

                  return (
                    <>
                      <path
                        d={pathD}
                        fill="none"
                        stroke="#21DDB8"
                        strokeWidth="0.5"
                      />
                      <path d={areaD} fill="url(#gradient)" />
                    </>
                  );
                })()}
              </svg>
            </div>
          </div>
          <div className="w-[calc(50%-0.5rem)] md:w-[calc(25%-0.75rem)] border-2 border-[#EFEFEF] rounded-xl flex flex-col justify-start items-start gap-1 py-2">
            <span className="text-[#1F263E] text-lg font-semibold px-4">
              Utilidad Neta
            </span>
            <span className="text-[#1F263E] text-2xl md:text-3xl font-semibold px-4">
              {" "}
              {utilidadNeta.dataActual}
            </span>
            {utilidadNeta.variacion !== null && (
              <span className={`${parseFloat(utilidadNeta.variacion) >= 0 ? "text-[#31C969]" : "text-[#FF6B8A]"} text-sm md:text-md font-base px-4`}>
                {parseFloat(utilidadNeta.variacion) >= 0 ? "" : ""}{utilidadNeta.variacion} desde el último periodo{" "}
              </span>
            )}
            <div className="w-full h-17 mt-1">
              <svg
                viewBox="0 0 100 20"
                className="w-full h-full"
                preserveAspectRatio="none"
              >
                <defs>
                  <linearGradient
                    id="gradient"
                    x1="0%"
                    y1="0%"
                    x2="0%"
                    y2="100%"
                  >
                    <stop offset="0%" stopColor="#21DDB8" stopOpacity="0.6" />
                    <stop offset="100%" stopColor="#21DDB8" stopOpacity="0.1" />
                  </linearGradient>
                </defs>
                {(() => {
                  // Convertir los datos a números
                  const years = Object.keys(
                    utilidadNeta.periodosAnteriores
                  ).sort();
                  const values = years.map((year) => {
                    const value = utilidadNeta.periodosAnteriores[year];
                    return parseFloat(value);
                  });

                  // Verificar si hay valores
                  if (values.length === 0) {
                    return null;
                  }

                  // Encontrar el valor máximo para normalizar
                  const maxValue = Math.max(...values) || 1; // Usar 1 como valor por defecto si maxValue es 0

                  // Verificar si hay valores válidos
                  if (maxValue === 0) {
                    return null;
                  }

                  // Crear puntos para la curva
                  const points = values.map((value, index) => {
                    const x = values.length === 1 ? 50 : (index / (values.length - 1)) * 100;
                    const y = 20 - (value / maxValue) * 15; // Reducir altura máxima a 15
                    return { x, y };
                  });

                  // Verificar si hay suficientes puntos para crear una curva
                  if (points.length === 0) {
                    return null; // No hay datos para mostrar
                  }

                  // Crear la curva
                  let pathD = `M${points[0].x},${points[0].y}`;

                  // Si solo hay un punto, dibujamos una línea horizontal
                  if (points.length === 1) {
                    pathD = `M0,${points[0].y} L100,${points[0].y}`;
                  } else {
                    // Crear curva con múltiples puntos
                    for (let i = 0; i < points.length - 1; i++) {
                      const cp1x =
                        points[i].x + (points[i + 1].x - points[i].x) / 3;
                      const cp1y = points[i].y;
                      const cp2x =
                        points[i].x + (2 * (points[i + 1].x - points[i].x)) / 3;
                      const cp2y = points[i + 1].y;
                      pathD += ` C${cp1x},${cp1y} ${cp2x},${cp2y} ${
                        points[i + 1].x
                      },${points[i + 1].y}`;
                    }
                  }

                  // Crear el área
                  const areaD = points.length === 1
                    ? `M0,${points[0].y} L100,${points[0].y} V20 H0 Z`
                    : `${pathD} V20 H${points[0].x} Z`;

                  return (
                    <>
                      <path
                        d={pathD}
                        fill="none"
                        stroke="#21DDB8"
                        strokeWidth="0.5"
                      />
                      <path d={areaD} fill="url(#gradient)" />
                    </>
                  );
                })()}
              </svg>
            </div>
          </div>
            </>
          )}
        </div>
      </div>
      <div className="w-full flex flex-col gap-2 justify-start items-center border-2 border-[#EFEFEF] rounded-xl p-4 mt-4 bg-gradient-to-b from-[#F8FAFB] from-0% via-[#F8FAFB] via-1% to-white to-50%">
        <div className="w-full flex justify-between items-center">
          <span className="text-[#1F263E] text-xl font-semibold px-5 mb-2">
            Periodos Contables
          </span>
          <div className="flex gap-2 items-center">
            <span className="text-sm text-[#1F263E]">Vista:</span>
            <div className="flex rounded-md overflow-hidden border border-[#E4E4E4]">
              <button
                className={`px-3 py-1 text-sm ${
                  vistaSeleccionada === "Anual"
                    ? "bg-[#1F263E] text-white"
                    : "bg-white text-[#1F263E]"
                }`}
                onClick={() => handleVistaChange("Anual")}
              >
                Anual
              </button>
              <button
                className={`px-3 py-1 text-sm ${
                  vistaSeleccionada === "Mensual"
                    ? "bg-[#1F263E] text-white"
                    : "bg-white text-[#1F263E]"
                }`}
                onClick={() => handleVistaChange("Mensual")}
              >
                Mensual
              </button>
            </div>

            {vistaSeleccionada === "Anual" && (
              <div className="flex items-center gap-2 ml-4">
                <span className="text-sm text-[#1F263E]">Mes:</span>
                <select
                  className="border border-[#E4E4E4] rounded-md p-1 text-sm outline-none"
                  value={mesSeleccionadoFiltro}
                  onChange={(e) => handleMesChange(e.target.value)}
                >
                  <option value="Todos">Todos</option>
                  <option value="Enero">Enero</option>
                  <option value="Febrero">Febrero</option>
                  <option value="Marzo">Marzo</option>
                  <option value="Abril">Abril</option>
                  <option value="Mayo">Mayo</option>
                  <option value="Junio">Junio</option>
                  <option value="Julio">Julio</option>
                  <option value="Agosto">Agosto</option>
                  <option value="Septiembre">Septiembre</option>
                  <option value="Octubre">Octubre</option>
                  <option value="Noviembre">Noviembre</option>
                  <option value="Diciembre">Diciembre</option>
                </select>
              </div>
            )}

            <div className="flex gap-2 ml-4">
              <button
                className={`text-gray-500 hover:text-gray-700 ${
                  startIndex === 0
                    ? "opacity-50 cursor-not-allowed"
                    : "cursor-pointer"
                }`}
                onClick={handlePrevMonth}
                disabled={startIndex === 0}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
              <button
                className={`text-gray-500 hover:text-gray-700 ${
                  startIndex >= datosFiltrados.length - maxVisibleMonths
                    ? "opacity-50 cursor-not-allowed"
                    : "cursor-pointer"
                }`}
                onClick={handleNextMonth}
                disabled={startIndex >= datosFiltrados.length - maxVisibleMonths}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <div className="w-full overflow-x-auto">
          {cargandoDatosFinancieros ? (
            <div className="w-full flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#21DDB8]"></div>
            </div>
          ) : errorDatosFinancieros ? (
            <div className="w-full flex justify-center items-center py-8 text-red-500">
              {errorDatosFinancieros}
            </div>
          ) : datosMensuales.length === 0 ? (
            <div className="w-full flex justify-center items-center py-8 text-gray-500">
              No hay datos disponibles para el periodo seleccionado
            </div>
          ) : (
            <table className="w-full border-collapse min-w-[640px]">
            <thead>
              <tr>
                <th className="py-3 px-4 text-left text-sm font-medium text-[#1F263E] border-b border-[#E4E4E4]">
                  <div className="flex items-center justify-between">
                    <span>Indicadores</span>
                    <button
                      className="bg-[#1F263E] text-white p-1 rounded-full hover:bg-[#3A4562] transition-colors"
                      onClick={() => abrirModal("nuevo")}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </button>
                  </div>
                </th>
                {visibleMonths.map((mes, index) => (
                  <th
                    key={index}
                    className="py-3 px-4 text-left text-sm font-medium text-[#1F263E] border-b border-[#E4E4E4]"
                  >
                    <div className="flex items-center justify-start gap-3">
                      <div>
                        <div className="font-medium">
                          {vistaSeleccionada === "Anual" && mesSeleccionadoFiltro === "Todos" ? (
                            mes.fecha.split("/")[2]
                          ) : vistaSeleccionada === "Anual" && mesSeleccionadoFiltro !== "Todos" ? (
                            mes.nombre + " " + mes.fecha.split("/")[2]
                          ) : (
                            mes.nombre
                          )}
                        </div>
                        <div className="text-xs text-gray-500">{mes.fecha}</div>
                      </div>
                      <div className="flex gap-1">
                        {/* Botón Ver */}
                        <button
                          className="text-gray-500 hover:text-gray-700 p-1 cursor-pointer"
                          onClick={() => {verPeriodo(mes.fecha)}}
                          title="Ver detalles"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                            <path
                              fillRule="evenodd"
                              d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </button>

                        {/* Botón Descargar */}
                        <button
                          className="text-gray-500 hover:text-gray-700 p-1 cursor-pointer"
                          onClick={() => navigate(RoutesPrivate.COMPARATIVA)}
                          title="Comparar"
                        >
                          <IconoComparativa size={"0.9rem"} color="#1F263E" />
                        </button>

                        {/* Botón Eliminar */}
                        <button
                          className="text-gray-500 hover:text-red-500 p-1 cursor-pointer"
                          onClick={() => eliminarPeriodo(mes.fecha)}
                          title="Eliminar"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </th>
                ))}
                <th className="py-3 px-4 text-left text-sm font-medium text-[#1F263E] border-b border-[#E4E4E4]">
                  Tendencia
                </th>
              </tr>
            </thead>
            <tbody>
              {nombresIndicadores.map((nombreIndicador, index) => (
                <tr key={index} className="border-b border-[#E4E4E4]">
                  <td className="py-3 px-4 text-sm font-medium text-[#1F263E]">
                    {obtenerNombreAmigable(nombreIndicador)}
                  </td>
                  {visibleMonths.map((mes, idx) => {
                    // Verificar si mes.indicadores existe y si contiene el indicador actual
                    if (!mes || !mes.indicadores) {
                      return (
                        <td key={idx} className="py-3 px-4">
                          <div className="flex flex-col">
                            <span className="text-sm font-semibold text-[#1F263E]">$0</span>
                            <span className="text-xs text-[#31C969]">0%</span>
                          </div>
                        </td>
                      );
                    }

                    const indicador = mes.indicadores[nombreIndicador];

                    // Si el indicador no existe, mostrar valores por defecto
                    if (!indicador) {
                      return (
                        <td key={idx} className="py-3 px-4">
                          <div className="flex flex-col">
                            <span className="text-sm font-semibold text-[#1F263E]">$0</span>
                            <span className="text-xs text-[#31C969]">0%</span>
                          </div>
                        </td>
                      );
                    }

                    return (
                      <td key={idx} className="py-3 px-4">
                        <div className="flex flex-col">
                          <span className="text-sm font-semibold text-[#1F263E]">
                            {indicador.valor || "$0"}
                          </span>
                          {indicador.porcentaje !== null && (
                            <span
                              className={`text-xs ${
                                indicador.tendencia === "positiva"
                                  ? "text-[#31C969]"
                                  : "text-[#FF6B8A]"
                              }`}
                            >
                              {indicador.porcentaje}
                            </span>
                          )}
                        </div>
                      </td>
                    );
                  })}
                  <td className="py-3 px-4 flex justify-center items-center">
                    <svg
                      viewBox="0 0 100 20"
                      className="w-28 h-10"
                      preserveAspectRatio="none"
                    >
                      {(() => {
                        // Verificar si hay datos mensuales
                        if (datosMensuales.length === 0) {
                          return null;
                        }

                        // Extraer valores numéricos para este indicador de todos los meses
                        const valores = datosMensuales
                          .map((mes) => {
                            // Verificar si el indicador existe para este mes
                            if (!mes.indicadores || !mes.indicadores[nombreIndicador]) {
                              return 0;
                            }

                            // Convertir el valor de string a número (quitar $ y comas)
                            const valorNumerico = parseFloat(
                              mes.indicadores[nombreIndicador].valor
                                .replace("$", "")
                                .replace(/,/g, "")
                            );
                            return isNaN(valorNumerico) ? 0 : valorNumerico;
                          })
                          .reverse(); // Invertir para que los meses más recientes estén a la derecha

                        // Encontrar el valor máximo para normalizar
                        const maxValor = Math.max(...valores) || 1; // Usar 1 como valor por defecto si maxValor es 0

                        // Verificar si hay valores válidos
                        if (valores.length === 0 || maxValor === 0) {
                          return null;
                        }

                        // Crear puntos para la curva
                        const puntos = valores.map((valor, i) => {
                          const x = valores.length === 1 ? 50 : (i / (valores.length - 1)) * 100;
                          // Normalizar el valor (invertido para que valores más altos estén más arriba)
                          const y = 20 - (valor / maxValor) * 15;
                          return { x, y };
                        });

                        // Verificar si hay suficientes puntos para crear una curva
                        if (puntos.length === 0) {
                          return null; // No hay datos para mostrar
                        }

                        // Crear la curva
                        let pathD = `M${puntos[0].x},${puntos[0].y}`;

                        // Si solo hay un punto, dibujamos una línea horizontal
                        if (puntos.length === 1) {
                          pathD = `M0,${puntos[0].y} L100,${puntos[0].y}`;
                        } else {
                          // Crear curva con múltiples puntos
                          for (let i = 0; i < puntos.length - 1; i++) {
                            const cp1x =
                              puntos[i].x + (puntos[i + 1].x - puntos[i].x) / 3;
                            const cp1y = puntos[i].y;
                            const cp2x =
                              puntos[i].x +
                              (2 * (puntos[i + 1].x - puntos[i].x)) / 3;
                            const cp2y = puntos[i + 1].y;
                            pathD += ` C${cp1x},${cp1y} ${cp2x},${cp2y} ${
                              puntos[i + 1].x
                            },${puntos[i + 1].y}`;
                          }
                        }

                        // Crear el área bajo la curva
                        const areaD = puntos.length === 1
                          ? `M0,${puntos[0].y} L100,${puntos[0].y} V20 H0 Z`
                          : `${pathD} L${puntos[puntos.length - 1].x},20 L${puntos[0].x},20 Z`;

                        // Verificar si existe el indicador y su color
                        let colorGrafico = "#21DDB8"; // Color por defecto

                        if (datosMensuales.length > 0 &&
                            datosMensuales[0].indicadores &&
                            datosMensuales[0].indicadores[nombreIndicador] &&
                            datosMensuales[0].indicadores[nombreIndicador].colorGrafico) {
                          colorGrafico = datosMensuales[0].indicadores[nombreIndicador].colorGrafico;
                        }

                        return (
                          <>
                            <path
                              d={pathD}
                              fill="none"
                              stroke={colorGrafico}
                              strokeWidth="1.5"
                            />
                            <path d={areaD} fill={`url(#gradient${index})`} />
                            <defs>
                              <linearGradient
                                id={`gradient${index}`}
                                x1="0%"
                                y1="0%"
                                x2="0%"
                                y2="100%"
                              >
                                <stop
                                  offset="0%"
                                  stopColor={colorGrafico}
                                  stopOpacity="0.6"
                                />
                                <stop
                                  offset="100%"
                                  stopColor={colorGrafico}
                                  stopOpacity="0.1"
                                />
                              </linearGradient>
                            </defs>
                          </>
                        );
                      })()}
                    </svg>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          )}
        </div>

        {/* Paginador */}
        {/* CustomPaginator ha sido eliminado */}

        {/* Modal para subir archivos */}
        <FileUploadModal
          isOpen={modalAbierto}
          onClose={cerrarModal}
          onUploadSuccess={() => {
            // Refrescar los datos financieros cuando se suban archivos exitosamente
            if (empresaSeleccionada) {
              const tipoPeriodo = vistaSeleccionada === "Anual" ? 1 : 3;
              const mesParaCargar = vistaSeleccionada === "Anual" ? mesSeleccionadoFiltro : null;
              cargarDatosFinancieros(tipoPeriodo, mesParaCargar);
            }
          }}
          title={`Subir archivo para ${mesSeleccionado || "nuevo periodo"}`}
        />
      </div>
    </div>
  );
};

export default Inicio;
