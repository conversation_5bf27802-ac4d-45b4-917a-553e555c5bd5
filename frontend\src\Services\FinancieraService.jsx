import axios from 'axios';
import Cookies from 'js-cookie';
import { decrypt } from './TokenService';
import API_GESTOR from '../assets/APIS/ApisAdministrador';
import { formatearFechaDDMMYYYY } from '../utils/dateUtils';

/**
 * Service for handling project-related API requests
 */
class FinancieraService {
  /**
   * Get the user ID from cookies
   * @returns {string|null} The user ID or null if not found
   */
  getUserId() {
    try {
      const userId = decrypt(Cookies.get("hora_llegadaFinanciera"));
      return userId;
    } catch (error) {
      console.error("Error getting user ID:", error);
      return null;
    }
  }

  getPerfil() {
    try {
      const perfil = decrypt(Cookies.get("rolFinanciera"));
      return perfil;
    } catch (error) {
      console.error("Error getting perfil:", error);
      return null;
    }
  }

  /**
   * Get the subscription ID from cookies
   * @returns {string|null} The subscription ID or null if not found
   */
  getSubscriptionId() {
    try {
      const subscriptionId = decrypt(Cookies.get("suscripcionFinanciera"));
      return subscriptionId;
    } catch (error) {
      console.error("Error getting subscription ID:", error);
      return null;
    }
  }

  /**
   * Get the company ID from cookies
   * @returns {number|null} The company ID or null if not found
   */
  getCompanyId() {
    try {
      const companyId = decrypt(Cookies.get("busFinanciera"));
      return parseInt(companyId);
    } catch (error) {
      console.error("Error getting company ID:", error);
      return null;
    }
  }

  /**
   * Get the authentication token from cookies
   * @returns {string|null} The token or null if not found
   */
  getToken() {
    try {
      const token = Cookies.get("TokenFinanciera");
      return token;
    } catch (error) {
      console.error("Error getting token:", error);
      return null;
    }
  }

  // Función async que obtenga la lista de empresas
  /**
   * Get the list of companies
   * @returns {Promise<Array>} Array of companies
   * @throws {Error} Custom error with message for 404 or other errors
   */
  async getEmpresas() {
    try {
      const idAplicacion = decrypt(Cookies.get("idAplicacionFinanciera"));
      const suscriptor = decrypt(Cookies.get("suscriptorFinanciera"));

      const response = await axios.get(API_GESTOR.ObtenerEmpresas(idAplicacion, suscriptor));

      if (response.status === 200) {
        return response.data;
      }
      return [];
    } catch (error) {
      console.error("Error fetching companies:", error);

      // Handle 404 error specifically
      if (error.response && error.response.status === 404) {
        const customError = new Error("No se encontraron empresas");
        customError.isNotFound = true;
        throw customError;
      }

      // For other errors, just propagate them
      throw error;
    }
  }

  /**
   * Get financial data for the last periods by company and period type
   * @param {number} tipoPeriodo - Period type (1: annual, 2: quarterly, 3: monthly, 4: weekly, 5: daily)
   * @returns {Promise<Object>} Financial data for the last periods
   * @throws {Error} Custom error with message for 404 or other errors
   */
  async getTotalAgrupadorUltimosPeriodos(tipoPeriodo) {
    try {
      const companyId = this.getCompanyId();
      if (!companyId) {
        throw new Error("No se ha seleccionado una empresa");
      }

      const token = this.getToken();
      if (!token) {
        throw new Error("No se ha encontrado el token de autenticación");
      }

      const response = await axios.get(
        API_GESTOR.TotalAgrupadorByEmpresaYPeriodo(companyId, tipoPeriodo),
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      if (response.status === 200) {
        // console.log("FinancieraService getTotalAgrupadorUltimosPeriodos response:", response.data.data);
        return response.data.data;
      }

      return null;
    } catch (error) {
      console.error("Error fetching financial data:", error);

      // Handle 404 error specifically
      if (error.response && error.response.status === 404) {
        const customError = new Error("No se encontraron datos financieros para el periodo seleccionado");
        customError.isNotFound = true;
        throw customError;
      }

      // For other errors, just propagate them
      throw error;
    }
  }

  /**
   * Get financial data for a specific month across years
   * @param {number} mesId - Month ID (1-12)
   * @returns {Promise<Object>} Financial data for the specific month across years
   * @throws {Error} Custom error with message for 404 or other errors
   */
  async getTotalAgrupadorPorMesAnual(mesId) {
    try {
      const companyId = this.getCompanyId();
      if (!companyId) {
        throw new Error("No se ha seleccionado una empresa");
      }

      const token = this.getToken();
      if (!token) {
        throw new Error("No se ha encontrado el token de autenticación");
      }
      // console.log("CompanyId:", companyId);
      // console.log("MesId:", mesId);
      const response = await axios.get(
        API_GESTOR.TotalAgrupadorPorMesAnual(companyId, mesId), // Use companyId instead of empresaId, and mesId instead of 1 (default)
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );
      // console.log("FinancieraService mesAnual:", response);

      if (response.status === 200) {
        // console.log("FinancieraService getTotalAgrupadorPorMesAnual response:", response.data);
        return response.data.data;
      }

      return null;
    } catch (error) {
      console.error("Error fetching financial data for specific month:", error);

      // Handle 404 error specifically
      if (error.response && error.response.status === 404) {
        const customError = new Error(`No se encontraron datos financieros para el mes ${mesId} en la empresa seleccionada`);
        customError.isNotFound = true;
        throw customError;
      }

      // For other errors, just propagate them
      throw error;
    }
  }

  /**
   * Upload Excel files for financial statements
   * @param {Array<File>} files - Array of Excel files to upload
   * @returns {Promise<Object>} Upload response
   * @throws {Error} Custom error with message for upload errors
   */
  async uploadEstadosFinancierosExcel(files) {
    try {
      const companyId = this.getCompanyId();
      const userId = this.getUserId();
      const token = this.getToken();

      if (!companyId) {
        throw new Error("No se ha seleccionado una empresa");
      }

      if (!userId) {
        throw new Error("No se ha encontrado el ID de usuario");
      }

      if (!token) {
        throw new Error("No se ha encontrado el token de autenticación");
      }

      if (!files || files.length === 0) {
        throw new Error("No se han seleccionado archivos para subir");
      }

      if (files.length > 10) {
        throw new Error("No se pueden subir más de 10 archivos a la vez");
      }
      // console.log("FinancieraService uploadEstadosFinancierosExcel files:", files);

      // Crear FormData para cada archivo y enviarlos
      const uploadPromises = files.map(async (file) => {
        const formData = new FormData();
        formData.append('int_idEmpresa', companyId.toString());
        formData.append('int_idUsuarios', userId.toString());
        formData.append('ef_nombre', file.name);
        formData.append('archivo', file);

        const response = await axios.post(
          API_GESTOR.UploadEstadosFinancierosExcel(),
          formData,
          {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'multipart/form-data'
            }
          }
        );

        return {
          fileName: file.name,
          success: response.status === 200 || response.status === 201,
          data: response.data
        };
      });

      // Esperar a que todos los archivos se suban
      const results = await Promise.allSettled(uploadPromises);

      // Procesar resultados
      const successfulUploads = [];
      const failedUploads = [];

      results.forEach((result, index) => {
        // console.log("FinancieraService uploadEstadosFinancierosExcel result:", result);
        if (result.status === 'fulfilled') {
          successfulUploads.push(result.value);
        } else {
          // Manejar errores específicos del backend
          let errorMessage = 'Error desconocido';

          if (result.reason && result.reason.response && result.reason.response.status === 400) {
            const errorData = result.reason.response.data;
            if (errorData && errorData.message && errorData.data) {
              // Error específico de estado financiero duplicado
              const estadoFinanciero = errorData.data;
              const fechaRegistroFormateada = formatearFechaDDMMYYYY(estadoFinanciero.dt_fechaRegistro);

              errorMessage = `${errorData.message}\n\n` +
                `Estado financiero existente:\n` +
                `• Nombre: ${estadoFinanciero.str_nombre}\n` +
                `• Fecha de registro: ${fechaRegistroFormateada}`;
            } else {
              errorMessage = result.reason.message || 'Formato de archivo no válido';
            }
          } else {
            errorMessage = result.reason.message || 'Error desconocido';
          }

          failedUploads.push({
            fileName: files[index].name,
            error: errorMessage
          });
        }
      });
      // console.log("FinancieraService uploadEstadosFinancierosExcel successfulUploads:", successfulUploads);
      // console.log("FinancieraService uploadEstadosFinancierosExcel failedUploads:", failedUploads);
      return {
        successful: successfulUploads,
        failed: failedUploads,
        totalFiles: files.length,
        successCount: successfulUploads.length,
        failureCount: failedUploads.length
      };

    } catch (error) {
      console.error("Error uploading Excel files:", error);

      // Handle specific error cases
      if (error.response && error.response.status === 413) {
        const customError = new Error("Los archivos son demasiado grandes. Reduce el tamaño e intenta nuevamente.");
        customError.isFileTooLarge = true;
        throw customError;
      }

      if (error.response && error.response.status === 400) {
        // Verificar si el error contiene información específica del backend
        if (error.response.data && error.response.data.message && error.response.data.data) {
          const errorData = error.response.data;
          const estadoFinanciero = errorData.data;

          // Formatear la fecha de registro usando dateUtils
          const fechaRegistroFormateada = formatearFechaDDMMYYYY(estadoFinanciero.dt_fechaRegistro);

          const mensajeDetallado = `${errorData.message}\n\n` +
            `Estado financiero existente:\n` +
            `• Nombre: ${estadoFinanciero.str_nombre}\n` +
            `• Fecha de registro: ${fechaRegistroFormateada}`;

          const customError = new Error(mensajeDetallado);
          customError.isBadRequest = true;
          customError.isDuplicateFinancialStatement = true;
          customError.existingData = estadoFinanciero;
          throw customError;
        } else {
          // Error 400 genérico
          const customError = new Error("Formato de archivo no válido. Solo se permiten archivos Excel (.xls, .xlsx)");
          customError.isBadRequest = true;
          throw customError;
        }
      }

      // For other errors, just propagate them
      throw error;
    }
  }

  /**
   * Delete a financial statement by ID
   * @param {number} estadoFinancieroId - ID of the financial statement to delete
   * @returns {Promise<Object>} Delete response
   * @throws {Error} Custom error with message for delete errors
   */
  async deleteEstadoFinanciero(estadoFinancieroId) {
    try {
      const token = this.getToken();
      if (!token) {
        throw new Error("No se ha encontrado el token de autenticación");
      }

      if (!estadoFinancieroId) {
        throw new Error("ID del estado financiero es requerido");
      }

      const response = await axios.delete(
        API_GESTOR.DeleteEstadoFinanciero(estadoFinancieroId),
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      if (response.status === 200 || response.status === 204) {
        return {
          success: true,
          message: "Estado financiero eliminado exitosamente",
          data: response.data
        };
      }

      return {
        success: false,
        message: "Error al eliminar el estado financiero"
      };
    } catch (error) {
      console.error("Error deleting financial statement:", error);

      // Handle specific error cases
      if (error.response && error.response.status === 404) {
        const customError = new Error("El estado financiero no fue encontrado");
        customError.isNotFound = true;
        throw customError;
      }

      if (error.response && error.response.status === 403) {
        const customError = new Error("No tienes permisos para eliminar este estado financiero");
        customError.isForbidden = true;
        throw customError;
      }

      if (error.response && error.response.status === 400) {
        const customError = new Error("Solicitud inválida. Verifica los datos enviados");
        customError.isBadRequest = true;
        throw customError;
      }

      // For other errors, just propagate them
      throw error;
    }
  }

  /**
   * Get financial statement periods by company and period type
   * @param {number} tipoPeriodo - Period type (1: annual, 2: quarterly, 3: monthly, 4: weekly, 5: daily)
   * @returns {Promise<Array>} Array of financial statement periods
   * @throws {Error} Custom error with message for 404 or other errors
   */
  async getEstadosFinancierosPeriodos(tipoPeriodo) {
    try {
      const companyId = this.getCompanyId();
      if (!companyId) {
        throw new Error("No se ha seleccionado una empresa");
      }

      const token = this.getToken();
      if (!token) {
        throw new Error("No se ha encontrado el token de autenticación");
      }

      const response = await axios.get(
        API_GESTOR.EstadosFinancierosPeriodos(companyId, tipoPeriodo),
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      if (response.status === 200) {
        // console.log("FinancieraService getEstadosFinancierosPeriodos response:", response.data.data);
        return response.data.data;
      }

      return [];
    } catch (error) {
      console.error("Error fetching financial statement periods:", error);

      // Handle 404 error specifically
      if (error.response && error.response.status === 404) {
        const customError = new Error("No se encontraron periodos de estados financieros para el tipo de periodo seleccionado");
        customError.isNotFound = true;
        throw customError;
      }

      // For other errors, just propagate them
      throw error;
    }
  }

  /**
   * Get financial ratios for a specific period
   * @param {number} tipoPeriodo - Period type (1: annual, 2: quarterly, 3: monthly, 4: weekly, 5: daily)
   * @param {string} fechaFin - End date in DD-MM-YYYY format
   * @returns {Promise<Array>} Array of financial ratios
   * @throws {Error} Custom error with message for 404 or other errors
   */
  async getRatiosEFPeriodoEspecifico(tipoPeriodo, fechaFin) {
    try {
      const companyId = this.getCompanyId();
      if (!companyId) {
        throw new Error("No se ha seleccionado una empresa");
      }

      const token = this.getToken();
      if (!token) {
        throw new Error("No se ha encontrado el token de autenticación");
      }

      if (!fechaFin) {
        throw new Error("La fecha fin es requerida");
      }

      const response = await axios.get(
        API_GESTOR.RatiosEFPeriodoEspecifico(companyId, tipoPeriodo, fechaFin),
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      if (response.status === 200) {
        // console.log("FinancieraService getRatiosEFPeriodoEspecifico response:", response.data);
        return response.data;
      }

      return [];
    } catch (error) {
      console.error("Error fetching financial ratios:", error);

      // Handle 404 error specifically
      if (error.response && error.response.status === 404) {
        const customError = new Error("No se encontraron ratios financieros para el periodo seleccionado");
        customError.isNotFound = true;
        throw customError;
      }

      // For other errors, just propagate them
      throw error;
    }
  }

  /**
   * Get financial data for specific period with last periods comparison
   * @param {number} tipoPeriodo - Period type (1: annual, 2: quarterly, 3: monthly, 4: weekly, 5: daily)
   * @param {string} fechaFin - End date in DD-MM-YYYY format
   * @returns {Promise<Array>} Financial data for specific period with historical comparison
   * @throws {Error} Custom error with message for 404 or other errors
   */
  async getTotalAgrupadorPeriodoEspecificoUltimosPeriodos(tipoPeriodo, fechaFin) {
    try {
      const companyId = this.getCompanyId();
      if (!companyId) {
        throw new Error("No se ha seleccionado una empresa");
      }

      const token = this.getToken();
      if (!token) {
        throw new Error("No se ha encontrado el token de autenticación");
      }

      if (!fechaFin) {
        throw new Error("La fecha fin es requerida");
      }

      // Convertir fecha de DD/MM/YYYY a DD-MM-YYYY para el endpoint
      const fechaFinFormateada = fechaFin.replace(/\//g, '-');

      const response = await axios.get(
        API_GESTOR.TotalAgrupadorPeriodoEspecificoUltimosPeriodos(companyId, tipoPeriodo, fechaFinFormateada),
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      if (response.status === 200) {
        // console.log("FinancieraService getTotalAgrupadorPeriodoEspecificoUltimosPeriodos response:", response.data);
        return response.data;
      }

      return null;
    } catch (error) {
      console.error("Error fetching financial data for specific period:", error);

      // Handle 404 error specifically
      if (error.response && error.response.status === 404) {
        const customError = new Error("No se encontraron datos financieros para el periodo específico seleccionado");
        customError.isNotFound = true;
        throw customError;
      }

      // For other errors, just propagate them
      throw error;
    }
  }

  /**
   * Get account details for financial statements by company, period type and end date
   * @param {number} tipoPeriodo - Period type (1: annual, 2: quarterly, 3: monthly, 4: weekly, 5: daily)
   * @param {string} fechaFin - End date in DD-MM-YYYY format
   * @returns {Promise<Array>} Array of financial statements with account details
   * @throws {Error} Custom error with message for 404 or other errors
   */
  async getEFCuentasPorEmpresaPeriodo(tipoPeriodo, fechaFin) {
    try {
      const companyId = this.getCompanyId();
      if (!companyId) {
        throw new Error("No se ha seleccionado una empresa");
      }

      const token = this.getToken();
      if (!token) {
        throw new Error("No se ha encontrado el token de autenticación");
      }

      if (!fechaFin) {
        throw new Error("La fecha fin es requerida");
      }

      // Convertir fecha de DD/MM/YYYY a DD-MM-YYYY para el endpoint
      const fechaFinFormateada = fechaFin.replace(/\//g, '-');

      const response = await axios.get(
        API_GESTOR.EFCuentasPorEmpresaPeriodo(companyId, tipoPeriodo, fechaFinFormateada),
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      if (response.status === 200) {
        // console.log("FinancieraService getEFCuentasPorEmpresaPeriodo response:", response.data);
        return response.data;
      }

      return [];
    } catch (error) {
      console.error("Error fetching account details for financial statements:", error);

      // Handle 404 error specifically
      if (error.response && error.response.status === 404) {
        const customError = new Error("No se encontraron cuentas de estados financieros para el periodo seleccionado");
        customError.isNotFound = true;
        throw customError;
      }

      // For other errors, just propagate them
      throw error;
    }
  }
}

export default new FinancieraService();
